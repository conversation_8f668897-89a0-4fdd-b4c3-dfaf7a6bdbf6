document.addEventListener('DOMContentLoaded', function() {
    const executeBtn = document.getElementById('executeBtn');
    const testBtn = document.getElementById('testBtn');
    const status = document.getElementById('status');

    // 检查当前页面
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const currentUrl = tabs[0].url;
        if (currentUrl && currentUrl.includes('cloud.xingchenjisu.com') && currentUrl.includes('OrderNew')) {
            status.textContent = '检测到主页面，可以开始执行';
            status.className = 'status success';
        } else {
            status.textContent = '请先打开主页面';
            status.className = 'status warning';
            executeBtn.disabled = true;
        }
    });

    executeBtn.addEventListener('click', function() {
        executeBtn.disabled = true;
        executeBtn.textContent = '执行中...';
        status.textContent = '正在处理订单数据，请稍候...';
        status.className = 'status info';

        // 向content script发送执行消息
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'execute'}, function(response) {
                if (chrome.runtime.lastError) {
                    console.log('发送消息失败:', chrome.runtime.lastError);
                    status.textContent = '发送消息失败，请刷新页面后重试';
                    status.className = 'status warning';
                    executeBtn.disabled = false;
                    executeBtn.textContent = '执行';
                } else {
                    console.log('消息发送成功');
                    status.textContent = '执行完成，请查看下载的Excel文件';
                    status.className = 'status success';

                    // 执行完成后恢复按钮
                    setTimeout(() => {
                        executeBtn.disabled = false;
                        executeBtn.textContent = '执行';
                        status.textContent = '准备就绪';
                        status.className = 'status info';
                    }, 5000);
                }
            });
        });
    });

    testBtn.addEventListener('click', function() {
        testBtn.disabled = true;
        testBtn.textContent = '测试中...';
        status.textContent = '正在测试单个订单数据提取...';
        status.className = 'status info';

        // 向content script发送测试消息
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            chrome.tabs.sendMessage(tabs[0].id, {action: 'testSingleOrder'}, function(response) {
                if (chrome.runtime.lastError) {
                    console.log('发送测试消息失败:', chrome.runtime.lastError);
                    status.textContent = '测试失败，请刷新页面后重试';
                    status.className = 'status warning';
                } else {
                    console.log('测试消息发送成功');
                    status.textContent = '测试完成，请查看新标签页和控制台日志';
                    status.className = 'status success';
                }

                // 恢复按钮
                setTimeout(() => {
                    testBtn.disabled = false;
                    testBtn.textContent = '测试单个订单';
                    status.textContent = '准备就绪';
                    status.className = 'status info';
                }, 3000);
            });
        });
    });
});
