// Background Script - 处理跨标签页通信

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {

    // 处理打开订单标签页的请求
    if (request.action === 'openOrderTab') {
        const orderNumber = request.orderNumber;
        const isTestMode = request.testMode || false; // 检查是否为测试模式
        const orderUrl = `http://cloud.xingchenjisu.com/#/OrderNew?tid=${orderNumber}&tradeType=all`;

        console.log(`打开订单标签页: ${orderUrl} (测试模式: ${isTestMode})`);

        // 在后台打开新标签页
        chrome.tabs.create({
            url: orderUrl,
            active: isTestMode // 测试模式下在前台打开，便于查看日志
        }, (tab) => {
            console.log(`新标签页已创建，ID: ${tab.id}, URL: ${orderUrl}`);

            // 等待页面加载完成后提取单号
            setTimeout(() => {
                console.log(`向标签页 ${tab.id} 发送提取消息`);

                chrome.tabs.sendMessage(tab.id, {
                    action: 'extractTrackingNumber',
                    expectedOrderNumber: orderNumber
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error(`发送消息到标签页 ${tab.id} 失败:`, chrome.runtime.lastError);
                        // 只有非测试模式才关闭标签页
                        if (!isTestMode) {
                            chrome.tabs.remove(tab.id);
                        }
                        sendResponse({
                            trackingNumber: '',
                            supplierExpress: '',
                            warehouseExpress: ''
                        });
                        return;
                    }

                    console.log(`收到标签页 ${tab.id} 的响应:`, response);

                    // 只有非测试模式才关闭标签页
                    if (!isTestMode) {
                        chrome.tabs.remove(tab.id, () => {
                            console.log(`标签页 ${tab.id} 已关闭`);
                        });
                    } else {
                        console.log(`测试模式：保持标签页 ${tab.id} 打开以便查看日志`);
                    }

                    // 返回结果
                    sendResponse({
                        trackingNumber: response ? response.trackingNumber : '',
                        supplierExpress: response ? response.supplierExpress : '',
                        warehouseExpress: response ? response.warehouseExpress : ''
                    });
                });
            }, 5500); // 增加等待时间到5.5秒
        });

        return true; // 保持消息通道开放
    }

    // 处理文件下载请求
    if (request.action === 'downloadFile') {
        chrome.downloads.download({
            url: request.url,
            filename: request.filename,
            saveAs: false // 直接下载到默认下载文件夹
        }, (downloadId) => {
            if (downloadId) {
                console.log(`文件下载开始，下载ID: ${downloadId}`);
                sendResponse({ success: true, downloadId: downloadId });
            } else {
                console.error('文件下载失败:', chrome.runtime.lastError);
                sendResponse({ success: false, error: chrome.runtime.lastError });
            }
        });
        return true; // 保持消息通道开放
    }

    // 处理获取物流信息的请求
    if (request.action === 'getLogistics') {
        const expressNumber = request.expressNumber;
        const testMode = request.testMode || false; // 检查是否为测试模式
        const baiduUrl = `https://www.baidu.com/s?ie=UTF-8&wd=${expressNumber}`;

        console.log(`打开百度物流查询页面: ${baiduUrl} (测试模式: ${testMode})`);

        // 在后台打开百度查询页面
        chrome.tabs.create({
            url: baiduUrl,
            active: testMode // 测试模式下在前台打开
        }, (tab) => {
            console.log(`百度查询标签页已创建，ID: ${tab.id}`);

            // 等待页面加载完成后提取物流信息
            setTimeout(() => {
                console.log(`向百度标签页 ${tab.id} 发送物流提取消息`);

                chrome.tabs.sendMessage(tab.id, {
                    action: 'extractLogistics',
                    expressNumber: expressNumber
                }, (response) => {
                    if (chrome.runtime.lastError) {
                        console.error(`发送消息到百度标签页 ${tab.id} 失败:`, chrome.runtime.lastError);
                        // 只有非测试模式才关闭标签页
                        if (!testMode) {
                            chrome.tabs.remove(tab.id);
                        }
                        sendResponse({
                            logistics: []
                        });
                        return;
                    }

                    console.log(`收到百度标签页 ${tab.id} 的物流响应:`, response);

                    // 只有非测试模式才关闭标签页
                    if (!testMode) {
                        chrome.tabs.remove(tab.id, () => {
                            console.log(`百度标签页 ${tab.id} 已关闭`);
                        });
                    } else {
                        console.log(`测试模式：保持百度标签页 ${tab.id} 打开以便查看日志`);
                    }

                    // 返回物流信息
                    sendResponse({
                        logistics: response ? response.logistics : []
                    });
                });
            }, 3000); // 等待3秒让百度页面加载
        });

        return true; // 保持消息通道开放
    }
});

// 处理标签页更新事件
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // 处理主页面
    if (changeInfo.status === 'complete' && tab.url && tab.url.includes('OrderNew')) {
        console.log(`订单页面加载完成，向标签页 ${tabId} 注入提取脚本`);
        console.log(`页面URL: ${tab.url}`);

        // 订单页面加载完成，注入提取脚本（内联版本以避免缓存问题）
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: function() {
                console.log('=== 新版本单号提取脚本已注入到页面 ===');
                console.log('当前页面URL:', window.location.href);
                console.log('页面标题:', document.title);

                // 监听来自background script的消息
                chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
                    console.log('收到提取消息:', request);

                    if (request.action === 'extractTrackingNumber') {
                        try {
                            const expectedOrderNumber = request.expectedOrderNumber;
                            console.log(`开始提取单号，期望订单编号: ${expectedOrderNumber}`);

                            // 等待页面完全加载
                            setTimeout(() => {
                                let trackingNumber = '';
                                let supplierExpress = '';  // 上家快递
                                let warehouseExpress = ''; // 仓库快递

                                console.log('=== 开始智能提取订单信息 ===');

                                // 1. 改进的快递单号识别函数（移到外层避免作用域问题）
                                const expressPatterns = [
                                    /^[A-Z]{2}\d+$/,           // JT1234567890 (极兔等)
                                    /^\d{10,}$/,              // 纯数字10位以上 (中通、申通等)
                                    /^[A-Z]+\d+$/,            // YT1234567890 (圆通等)
                                    /^\d{8,15}$/              // 8-15位数字 (邮政等)
                                ];

                                function isExpressNumber(text) {
                                    if (!text || typeof text !== 'string') return false;
                                    return expressPatterns.some(pattern => pattern.test(text.trim()));
                                }

                                // 2. 查找有工单的子订单容器
                                console.log('查找有工单的子订单...');
                                const workOrderButtons = document.querySelectorAll('button');
                                let targetContainer = null;

                                for (const button of workOrderButtons) {
                                    if (button.textContent.includes('查看工单')) {
                                        console.log('找到查看工单按钮');
                                        // 向上查找包含快递信息的容器
                                        targetContainer = button.closest('.tablePro-body-item') ||
                                                         button.closest('.transfer') ||
                                                         button.closest('[data-v-b37e1c92]');

                                        if (targetContainer) {
                                            console.log('找到工单对应的容器');
                                            break;
                                        }
                                    }
                                }

                                // 3. 查找单号和快递信息
                                let subOrderContainer = null;
                                let searchContainer = document;

                                if (targetContainer) {
                                    // 关键：从工单容器向上查找到.tablePro-body，然后找到包含单号的子订单容器
                                    console.log('从工单容器向上查找包含单号的子订单容器...');

                                    // 1. 向上找到 .tablePro-body
                                    const tableProBody = targetContainer.closest('.tablePro-body');

                                    if (tableProBody) {
                                        console.log('找到 .tablePro-body 容器');

                                        // 2. 在其中查找所有 .tablePro-body-item
                                        const allItems = tableProBody.querySelectorAll('.tablePro-body-item');
                                        console.log(`找到 ${allItems.length} 个子订单容器`);

                                        // 3. 找到包含单号但不包含工单的容器
                                        for (const item of allItems) {
                                            const itemText = item.textContent;
                                            const hasTrackingNumber = itemText.includes('单号：');
                                            const hasWorkOrder = itemText.includes('查看工单');

                                            console.log(`检查容器: 包含单号=${hasTrackingNumber}, 包含工单=${hasWorkOrder}`);

                                            if (hasTrackingNumber && !hasWorkOrder) {
                                                subOrderContainer = item;
                                                console.log('✅ 找到包含单号的子订单容器');
                                                break;
                                            }
                                        }
                                    }

                                    if (subOrderContainer) {
                                        searchContainer = subOrderContainer;
                                        console.log('在包含单号的子订单容器中查找单号');
                                    } else {
                                        console.log('未找到包含单号的子订单容器，在工单容器中查找单号');
                                        searchContainer = targetContainer;
                                    }
                                } else {
                                    console.log('未找到工单容器，在整个页面中查找单号');
                                    searchContainer = document;
                                }

                                // 在确定的容器中查找单号 - 增强调试
                                console.log('=== 开始详细单号查找调试 ===');

                                // 1. 查找所有可能包含单号的元素
                                const allElements = searchContainer.querySelectorAll('span, div, a');
                                console.log(`在容器中找到 ${allElements.length} 个可能的元素`);

                                // 2. 查找所有包含数字-数字格式的文本
                                const possibleTrackingNumbers = [];
                                for (const element of allElements) {
                                    const text = element.textContent.trim();
                                    // 更宽松的匹配：包含数字-数字的文本
                                    if (text.match(/\d+-\d+/) && text.length > 10) {
                                        possibleTrackingNumbers.push({
                                            element: element.tagName,
                                            text: text,
                                            matches: text.match(/\d{6}-\d+/g)
                                        });
                                    }
                                }

                                console.log('找到的可能单号:', possibleTrackingNumbers);

                                // 3. 使用原有逻辑查找单号
                                const trackingElements = searchContainer.querySelectorAll('span');
                                for (const element of trackingElements) {
                                    const text = element.textContent.trim();
                                    // 匹配单号格式：6位数字-长数字
                                    if (text.match(/^\d{6}-\d+$/)) {
                                        trackingNumber = text;
                                        console.log(`✅ 找到单号: ${trackingNumber}`);
                                        break;
                                    }
                                }

                                // 4. 如果没找到，尝试更宽松的查找
                                if (!trackingNumber && possibleTrackingNumbers.length > 0) {
                                    console.log('使用宽松匹配查找单号...');
                                    for (const item of possibleTrackingNumbers) {
                                        if (item.matches && item.matches.length > 0) {
                                            trackingNumber = item.matches[0];
                                            console.log(`✅ 宽松匹配找到单号: ${trackingNumber}`);
                                            break;
                                        }
                                    }
                                }

                                // 5. 最后的备用方案：在整个页面中查找单号
                                if (!trackingNumber) {
                                    console.log('备用方案：在整个页面中查找单号...');
                                    const allPageElements = document.querySelectorAll('span, div');
                                    for (const element of allPageElements) {
                                        const text = element.textContent.trim();
                                        // 查找单号格式
                                        if (text.match(/^\d{6}-\d+$/) && text.length > 15) {
                                            trackingNumber = text;
                                            console.log(`✅ 备用方案找到单号: ${trackingNumber}`);
                                            break;
                                        }
                                    }
                                }

                                console.log('=== 单号查找调试结束 ===');

                                // 4. 在工单容器中查找快递信息（快递信息确实在工单容器中）
                                const expressSearchContainer = targetContainer || document;
                                console.log(`在${targetContainer ? '工单容器' : '整个页面'}中查找快递信息`);

                                const allDivs = expressSearchContainer.querySelectorAll('div');
                                console.log(`找到 ${allDivs.length} 个div元素`);

                                // 存储找到的所有快递信息，用于调试
                                const foundExpress = [];

                                // 分别查找上家快递和仓库快递，确保不重复
                                for (const div of allDivs) {
                                    const text = div.textContent;

                                    // 查找上家快递 - 确保div只包含上家快递信息
                                    if (text.includes('上家快递：') && !text.includes('仓库快递：') && !supplierExpress) {
                                        console.log('找到上家快递div:', text.substring(0, 200));
                                        const links = div.querySelectorAll('a');

                                        for (const link of links) {
                                            const linkText = link.textContent.trim();
                                            console.log(`检查上家快递链接: ${linkText}`);

                                            if (isExpressNumber(linkText)) {
                                                // 确保这个单号还没有被仓库快递使用
                                                if (linkText !== warehouseExpress) {
                                                    supplierExpress = linkText;
                                                    foundExpress.push({type: '上家快递', number: linkText});
                                                    console.log(`✅ 提取到上家快递: ${supplierExpress}`);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }

                                // 单独循环查找仓库快递，避免与上家快递混淆
                                for (const div of allDivs) {
                                    const text = div.textContent;

                                    // 查找仓库快递 - 确保div只包含仓库快递信息
                                    if (text.includes('仓库快递：') && !text.includes('上家快递：') && !warehouseExpress) {
                                        console.log('找到仓库快递div:', text.substring(0, 200));
                                        const links = div.querySelectorAll('a');

                                        for (const link of links) {
                                            const linkText = link.textContent.trim();
                                            console.log(`检查仓库快递链接: ${linkText}`);

                                            if (isExpressNumber(linkText)) {
                                                // 确保这个单号还没有被上家快递使用
                                                if (linkText !== supplierExpress) {
                                                    warehouseExpress = linkText;
                                                    foundExpress.push({type: '仓库快递', number: linkText});
                                                    console.log(`✅ 提取到仓库快递: ${warehouseExpress}`);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }

                                // 5. 验证和输出调试信息
                                console.log('=== 提取结果汇总 ===');
                                console.log(`单号: ${trackingNumber || '未找到'}`);
                                console.log(`上家快递: ${supplierExpress || '未找到'}`);
                                console.log(`仓库快递: ${warehouseExpress || '未找到'}`);
                                console.log('找到的所有快递信息:', foundExpress);

                                // 检查是否有重复的快递单号
                                if (supplierExpress && warehouseExpress && supplierExpress === warehouseExpress) {
                                    console.error('❌ 检测到重复的快递单号！上家快递和仓库快递相同:', supplierExpress);
                                    // 清空其中一个，优先保留仓库快递
                                    supplierExpress = '';
                                    console.log('已清空上家快递，保留仓库快递');
                                }

                                if (!supplierExpress && !warehouseExpress) {
                                    console.warn('⚠️ 未找到任何快递信息，输出调试信息');
                                    // 输出页面结构用于调试
                                    const allLinks = document.querySelectorAll('a');
                                    const allLinkTexts = Array.from(allLinks)
                                        .map(link => link.textContent.trim())
                                        .filter(text => text.length > 5 && /\d/.test(text) && isExpressNumber(text));
                                    console.log('页面中所有符合格式的快递单号:', allLinkTexts);

                                    // 如果有快递单号但没有正确分类，尝试备用方案
                                    if (allLinkTexts.length > 0) {
                                        console.log('尝试备用提取方案...');
                                        // 如果只有一个快递单号，作为仓库快递
                                        if (allLinkTexts.length === 1) {
                                            warehouseExpress = allLinkTexts[0];
                                            console.log(`备用方案：单个快递单号作为仓库快递: ${warehouseExpress}`);
                                        }
                                        // 如果有两个快递单号，第一个作为上家快递，第二个作为仓库快递
                                        else if (allLinkTexts.length >= 2) {
                                            supplierExpress = allLinkTexts[0];
                                            warehouseExpress = allLinkTexts[1];
                                            console.log(`备用方案：第一个作为上家快递: ${supplierExpress}，第二个作为仓库快递: ${warehouseExpress}`);
                                        }
                                    }
                                }

                                sendResponse({
                                    trackingNumber: trackingNumber,
                                    supplierExpress: supplierExpress,
                                    warehouseExpress: warehouseExpress
                                });

                            }, 2500); // 等待2.5秒确保页面加载完成

                            return true; // 保持消息通道开放
                        } catch (error) {
                            console.error('提取单号时出错:', error);
                            sendResponse({
                                trackingNumber: '',
                                supplierExpress: '',
                                warehouseExpress: ''
                            });
                        }
                    }
                });
            }
        }, (result) => {
            if (chrome.runtime.lastError) {
                console.error(`脚本注入失败:`, chrome.runtime.lastError);
            } else {
                console.log(`脚本已成功注入到标签页 ${tabId}，结果:`, result);
            }
        });
    }

    // 处理百度物流查询页面
    else if (changeInfo.status === 'complete' && tab.url && tab.url.includes('baidu.com/s') && tab.url.includes('wd=')) {
        console.log(`百度物流查询页面加载完成，向标签页 ${tabId} 注入物流提取脚本`);

        // 注入物流信息提取脚本
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: function() {
                console.log('=== 百度物流信息提取脚本已注入 ===');
                console.log('当前页面URL:', window.location.href);

                // 监听来自background script的消息
                chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
                    console.log('收到物流提取消息:', request);

                    if (request.action === 'extractLogistics') {
                        try {
                            const expressNumber = request.expressNumber;
                            console.log(`开始提取物流信息，快递单号: ${expressNumber}`);

                            // 等待页面完全加载
                            setTimeout(() => {
                                const logistics = [];

                                // 查找物流追踪信息
                                const trackItems = document.querySelectorAll('.show-track-item_2GBSV');
                                console.log(`找到 ${trackItems.length} 条物流记录`);

                                // 提取前3条物流信息
                                for (let i = 0; i < Math.min(3, trackItems.length); i++) {
                                    const item = trackItems[i];

                                    const timeElement = item.querySelector('.show-track-item-content-time_2z0Im');
                                    const messageElement = item.querySelector('.show-track-item-content-message_795I5');

                                    if (timeElement && messageElement) {
                                        const time = timeElement.textContent.trim();
                                        const message = messageElement.textContent.trim();

                                        logistics.push({
                                            time: time,
                                            message: message
                                        });

                                        console.log(`提取物流信息 ${i + 1}: ${time} - ${message.substring(0, 50)}...`);
                                    }
                                }

                                console.log(`物流信息提取完成，共 ${logistics.length} 条`);

                                sendResponse({
                                    logistics: logistics
                                });

                            }, 2000); // 等待2秒确保页面加载完成

                            return true; // 保持消息通道开放
                        } catch (error) {
                            console.error('提取物流信息时出错:', error);
                            sendResponse({
                                logistics: []
                            });
                        }
                    }
                });
            }
        }, (result) => {
            if (chrome.runtime.lastError) {
                console.error(`百度物流脚本注入失败:`, chrome.runtime.lastError);
            } else {
                console.log(`百度物流脚本已成功注入到标签页 ${tabId}`);
            }
        });
    }
});

// 旧的函数已删除，现在使用内联版本

console.log('Background script loaded');
