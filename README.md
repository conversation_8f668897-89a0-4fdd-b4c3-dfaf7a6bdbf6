# 工单批阅助手 Chrome 扩展

这是一个专为 http://cloud.xingchenjisu.com 工单系统设计的自动化数据抓取工具。

## 功能特性

- 🎯 自动点击"待确认"工单标签
- 📋 自动提取工单列表中的订单编号
- 🔗 自动在新标签页获取订单详细单号
- 💬 自动提取工单详情中的沟通记录（最后三条）
- 📄 支持多页工单自动翻页处理
- ⚡ 自动设置每页100条，减少翻页次数
- 🔄 后台标签页操作，不影响当前浏览
- 📊 自动导出Excel文件（XLSX格式）
- 📝 每个工单占据三行，格式清晰易读
- 🚚 自动提取仓库快递单号信息
- 📈 实时进度提示，显示处理进度
- 🔧 Excel单元格自动换行，内容完整显示

## 安装方法

1. 打开Chrome浏览器
2. 在地址栏输入 `chrome://extensions/`
3. 打开右上角的"开发者模式"开关
4. 点击"加载已解压的扩展程序"
5. 选择包含这些文件的文件夹
6. 扩展安装完成，会在工具栏显示图标

## 使用方法

1. 打开工单页面：http://cloud.xingchenjisu.com/#/workorder
2. 点击浏览器工具栏中的扩展图标
3. 点击"执行"按钮
4. 扩展将自动：
   - 点击"待确认"标签
   - 设置每页显示100条（减少翻页次数）
   - 显示进度提示框
   - 逐个处理工单数据
   - 自动翻页处理所有工单
   - 导出XLSX格式Excel文件到下载文件夹
   - 自动关闭临时标签页，避免浏览器负担

## 导出格式说明

Excel文件（XLSX格式）包含三列：
- **订单编号**：从工单列表提取
- **单号**：从订单详情页面提取（第1行显示订单单号，第2行显示上家快递单号，第3行显示仓库快递单号）
- **工单详情**：所有沟通记录（动态数量，不限制条数）

每个工单的格式：
```
订单编号              单号                    工单详情
2881972706517100000  250821-050908403343459  武汉仓-陈丰: 2025-08-22 15:25:28
上家快递              JT5404927438114         无法源单退回，麻烦留退回地址
仓库快递              JT3131253165621         ******8383: 2025-08-26 00:20:30
                                             退回地址：陈亮 15391935571...
                                             ******8383: 2025-08-26 00:20:30
                                             完结工单
                                             [更多沟通记录...]
[空白行]
```

**格式特点：**
- 表头使用粗体格式
- 订单编号列显示"上家快递"和"仓库快递"标签
- 动态保存所有沟通记录（不限制数量）
- 每个订单之间用空白行分隔

### 🔢 数字格式保护

- **订单编号**和**单号**均设置为文本格式，确保长数字完整显示
- 避免Excel自动转换为科学计数法
- 保持数据的完整性和可读性

### 🔄 自动标签页管理

- 每个订单查询完成后自动关闭临时标签页
- 避免打开过多标签页造成浏览器卡顿
- 后台操作，不影响用户当前浏览

### ⚡ 性能优化

- **智能分页设置**：自动将每页显示数量从10条调整为100条
- **减少翻页次数**：113个工单从12页减少到2页，大幅提升处理速度
- **高效处理**：减少页面加载等待时间，提高整体执行效率

### 📈 实时进度显示

- 页面中央显示轻量化半透明进度提示框
- 不会让背景变暗，不影响页面浏览
- 实时显示"正在保存第 X/总数 个订单"
- 进度条直观显示完成百分比
- 处理完成后自动关闭提示框

### 📋 Excel格式优化

- 工单详情列自动换行显示
- 根据内容自动调整行高
- 打开Excel文件即可完整查看所有内容
- 无需手动调整单元格格式
- 智能识别有工单的子订单，提取正确的单号
- 自动提取仓库快递单号信息

## 文件结构

```
├── manifest.json      # 扩展配置文件
├── popup.html        # 弹窗界面
├── popup.js          # 弹窗逻辑
├── content.js        # 页面内容脚本
├── background.js     # 后台服务脚本
├── icon16.png        # 16x16 图标
├── icon48.png        # 48x48 图标
├── icon128.png       # 128x128 图标
└── README.md         # 说明文档
```

## 常见问题

### Q: Excel文件没有自动下载怎么办？
A: 这可能是由于以下原因：
1. **浏览器弹窗拦截**：请允许该网站的弹窗
2. **下载被阻止**：检查浏览器下载设置，允许自动下载
3. **HTTP协议限制**：由于网站使用HTTP而非HTTPS，某些浏览器可能阻止下载

**解决方案**：
- 在Chrome设置中允许该网站的弹窗和下载
- 如果仍然失败，扩展会显示提示信息，请按提示操作

### Q: 提示"未能获取到单号"怎么办？
A: 这通常是因为：
1. 订单页面加载较慢
2. 订单没有对应的子订单信息
3. 网络连接问题

**解决方案**：
- 等待网络稳定后重新执行
- 检查订单是否确实存在单号信息

## 版本信息

- 版本：1.0
- 更新日期：2025-08-26
