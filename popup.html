<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 280px;
            padding: 15px;
            font-family: Arial, sans-serif;
        }
        .button {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 12px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            cursor: pointer;
            border-radius: 4px;
            width: 100%;
            margin-bottom: 10px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
        }
        .status.info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #0066cc;
        }
        .status.success {
            background-color: #e7f5e7;
            border: 1px solid #b3d9b3;
            color: #006600;
        }
        .status.warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .instructions {
            font-size: 11px;
            color: #666;
            margin-bottom: 10px;
            line-height: 1.4;
        }
    </style>
</head>
<body>
    <h3>星辰导出超时订单</h3>

    <div class="instructions">
        请先打开主页面，然后点击执行按钮开始自动处理未打印订单数据。
    </div>

    <div class="status info" id="status">
        准备就绪
    </div>

    <button class="button" id="executeBtn">执行</button>
    <button class="button" id="testBtn" style="background-color: #ff9800; margin-top: 5px;">测试单个订单</button>

    <script src="popup.js"></script>
</body>
</html>
