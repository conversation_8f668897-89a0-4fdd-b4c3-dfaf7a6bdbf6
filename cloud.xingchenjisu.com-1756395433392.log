 VERSION: prod
 主页面订单抓取助手脚本已加载
 工单批阅助手脚本已加载
 Object
 === 新版本单号提取脚本已注入到页面 ===
 当前页面URL: http://cloud.xingchenjisu.com/#/OrderNew
 页面标题: 一键下单
 === 新版本单号提取脚本已注入到页面 ===
 当前页面URL: http://cloud.xingchenjisu.com/#/OrderNew
 页面标题: 一键下单
 248
 收到执行命令，模式: refunded
 开始执行订单数据抓取，模式: refunded...
 查找已出单标签页...
 找到已出单标签页，点击...
 已点击已出单标签页
 收到提取消息: Object
 设置每页显示100条...
 找到100条/页选项，点击...
 已设置为每页显示100条
 分页信息 - 总记录: 132, 当前页: 1, 总页数: 2
 开始处理 2 页订单数据
 
=== 处理第 1 页 ===
 查找订单，模式: 已退款未打印...
 订单 2895603099608742680 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895603099608742680
 订单 2895723050536207661 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895723050536207661
 订单 2895720890834077458 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895720890834077458
 订单 2895717686127170489 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895717686127170489
 订单 4702958857297059748 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702958857297059748
 订单 4702947121589726817 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702947121589726817
 订单 2895554679737346450 有 2 个真正的商品子订单
 子订单 1 未退款 - 订单号: 2895554679737346450
 子订单 2 未退款 - 订单号: 2895554679737346450
 跳过全部子订单已退款的订单 - 订单号: 2895554679737346450
 订单 2895735360891362496 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895735360891362496
 订单 4703039676009958747 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4703039676009958747
 订单 2895711852065055577 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895711852065055577
 订单 4702780945281560734 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702780945281560734
 订单 4702864753642281023 有 1 个真正的商品子订单
 找到有效单子订单（已退款、未打印） - 订单号: 4702864753642281023
 订单 2895598202102651354 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895598202102651354
 订单 2895587294035877985 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895587294035877985
 订单 4702804849930800036 有 1 个真正的商品子订单
 找到有效单子订单（已退款、未打印） - 订单号: 4702804849930800036
 订单 4702712546040247648 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702712546040247648
 订单 2895553309013213380 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895553309013213380
 订单 2895387567547246762 有 2 个真正的商品子订单
 子订单 1 未退款 - 订单号: 2895387567547246762
 子订单 2 未退款 - 订单号: 2895387567547246762
 跳过全部子订单已退款的订单 - 订单号: 2895387567547246762
 订单 4702664774031005512 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702664774031005512
 订单 4702651778634055649 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702651778634055649
 订单 2895506437381325570 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895506437381325570
 订单 4702814532818611408 有 1 个真正的商品子订单
 找到有效单子订单（已退款、未打印） - 订单号: 4702814532818611408
 订单 2895446210866648260 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895446210866648260
 订单 2895509604812804298 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895509604812804298
 订单 4702573190071713227 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702573190071713227
 订单 4702567034927034718 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702567034927034718
 订单 2895439621866496388 有 1 个真正的商品子订单
 找到有效单子订单（已退款、未打印） - 订单号: 2895439621866496388
 订单 4702773672975097204 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702773672975097204
 订单 4702722519585741818 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702722519585741818
 订单 4702718883616887716 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702718883616887716
 订单 2895419676280657664 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895419676280657664
 订单 6920926659358981885 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 6920926659358981885
 订单 4702511305137045918 有 2 个真正的商品子订单
 子订单 1 未退款 - 订单号: 4702511305137045918
 子订单 2 未退款 - 订单号: 4702511305137045918
 跳过全部子订单已退款的订单 - 订单号: 4702511305137045918
 订单 4702362662279356306 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702362662279356306
 订单 4702445497037177611 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702445497037177611
 订单 4702497480410810109 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702497480410810109
 订单 4702281374126566332 有 3 个真正的商品子订单
 子订单 1 未退款 - 订单号: 4702281374126566332
 子订单 2 未退款 - 订单号: 4702281374126566332
 子订单 3 有效（已退款、未打印） - 订单号: 4702281374126566332
 找到有效多子订单 (1/3个有效) - 订单号: 4702281374126566332
 订单 2895169513249140662 有 2 个真正的商品子订单
 子订单 1 未退款 - 订单号: 2895169513249140662
 子订单 2 未退款 - 订单号: 2895169513249140662
 跳过全部子订单已退款的订单 - 订单号: 2895169513249140662
 订单 2895116738435787754 有 3 个真正的商品子订单
 子订单 1 未退款 - 订单号: 2895116738435787754
 子订单 2 未退款 - 订单号: 2895116738435787754
 子订单 3 未退款 - 订单号: 2895116738435787754
 跳过全部子订单已退款的订单 - 订单号: 2895116738435787754
 订单 2894989983358012982 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2894989983358012982
 订单 2895088082026689074 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895088082026689074
 订单 2895140424904353691 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2895140424904353691
 订单 4702186622837886028 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702186622837886028
 订单 2894926227450943351 有 3 个真正的商品子订单
 子订单 1 未退款 - 订单号: 2894926227450943351
 子订单 2 未退款 - 订单号: 2894926227450943351
 子订单 3 未退款 - 订单号: 2894926227450943351
 跳过全部子订单已退款的订单 - 订单号: 2894926227450943351
 订单 2895037465789782970 有 2 个真正的商品子订单
 子订单 1 未退款 - 订单号: 2895037465789782970
 子订单 2 未退款 - 订单号: 2895037465789782970
 跳过全部子订单已退款的订单 - 订单号: 2895037465789782970
 订单 2894995381300443492 有 1 个真正的商品子订单
 找到有效单子订单（已退款、未打印） - 订单号: 2894995381300443492
 订单 2894959345864803160 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2894959345864803160
 订单 4702230579500022605 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702230579500022605
 订单 4702074193742019540 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4702074193742019540
 订单 2894996676065043370 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2894996676065043370
 订单 2894922734639663882 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2894922734639663882
 订单 2894646541431683776 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2894646541431683776
 共找到 6 个需要处理的订单
 第 1 页找到 6 个需要处理的订单
 处理第 1 页第 1 个订单 - 4702864753642281023 (single)
 在页面中找到单号: 250825-427745625751619
 在页面中找到上家快递: 777334661478633
 在页面中找到仓库快递: YT7566728253109
 提取到成交时间: 2025-08-25
 提取到商品ID: 959760257564
 提取到单号: 250825-427745625751619
 提取到上家快递: 777334661478633
 提取到仓库快递: YT7566728253109
 提取到商品ID: 959760257564
 提取到成交时间: 2025-08-25
 获取上家快递物流信息: 777334661478633
 准备获取快递物流信息: 777334661478633 (测试模式: false)
 获取到物流信息: Object
 订单处理完成: Object
 处理第 1 页第 2 个订单 - 4702804849930800036 (single)
 在页面中找到单号: 250825-257399211654047
 在页面中找到上家快递: 9809260416324
 在页面中找到仓库快递: YT7566724036054
 提取到成交时间: 2025-08-25
 提取到商品ID: 824996474547
 提取到单号: 250825-257399211654047
 提取到上家快递: 9809260416324
 提取到仓库快递: YT7566724036054
 提取到商品ID: 824996474547
 提取到成交时间: 2025-08-25
 获取上家快递物流信息: 9809260416324
 准备获取快递物流信息: 9809260416324 (测试模式: false)
 获取到物流信息: Object
 订单处理完成: Object
 处理第 1 页第 3 个订单 - 4702814532818611408 (single)
 在页面中找到单号: 250825-334726448774047
 在页面中找到上家快递: YT8790370579161
 在页面中找到仓库快递: YT7566718208191
 提取到成交时间: 2025-08-25
 提取到商品ID: 939695588103
 提取到单号: 250825-334726448774047
 提取到上家快递: YT8790370579161
 提取到仓库快递: YT7566718208191
 提取到商品ID: 939695588103
 提取到成交时间: 2025-08-25
 获取上家快递物流信息: YT8790370579161
 准备获取快递物流信息: YT8790370579161 (测试模式: false)
 获取到物流信息: Object
 订单处理完成: Object
 处理第 1 页第 4 个订单 - 2895439621866496388 (single)
 在页面中找到单号: 250825-064985536060582
 在页面中找到上家快递: 78531144174429
 在页面中找到仓库快递: YT7566719174385
 提取到成交时间: 2025-08-25
 提取到商品ID: 962145604149
 提取到单号: 250825-064985536060582
 提取到上家快递: 78531144174429
 提取到仓库快递: YT7566719174385
 提取到商品ID: 962145604149
 提取到成交时间: 2025-08-25
 获取上家快递物流信息: 78531144174429
 准备获取快递物流信息: 78531144174429 (测试模式: false)
 获取到物流信息: Object
 订单处理完成: Object
 处理第 1 页第 5 个订单 - 4702281374126566332 (multiple)
 处理子订单 3
 找到 3 个不同的单号: Array(3)
 子订单3对应的单号: 250825-579254361663546
 找到 3 个不同的上家快递: Array(3)
 子订单3对应的上家快递: 777334671051235
 找到 3 个不同的仓库快递: Array(3)
 子订单3对应的仓库快递: YT7566689521279
 从子订单提取到商品ID: 920136767432
 提取到成交时间: 2025-08-25
 提取到商品ID: 941448303550
 子订单3 - 单号: 250825-579254361663546
 子订单3 - 上家快递: 777334671051235
 子订单3 - 仓库快递: YT7566689521279
 子订单3 - 商品ID: 920136767432
 子订单3 - 成交时间: 2025-08-25
 获取子订单3上家快递物流信息: 777334671051235
 准备获取快递物流信息: 777334671051235 (测试模式: false)
 获取到物流信息: Object
 子订单3处理完成: Object
 处理第 1 页第 6 个订单 - 2894995381300443492 (single)
 在页面中找到单号: 250825-615068505861190
 在页面中找到上家快递: 78531077461068
 在页面中找到仓库快递: YT7566653607666
 提取到成交时间: 2025-08-25
 提取到商品ID: 942798635767
 提取到单号: 250825-615068505861190
 提取到上家快递: 78531077461068
 提取到仓库快递: YT7566653607666
 提取到商品ID: 942798635767
 提取到成交时间: 2025-08-25
 获取上家快递物流信息: 78531077461068
 准备获取快递物流信息: 78531077461068 (测试模式: false)
 获取到物流信息: Object
 订单处理完成: Object
 下一页按钮状态: 可用
 第 1 页处理完成，准备跳转到下一页...
 点击下一页按钮...
 已跳转到第 2 页
 已跳转到第 2 页，等待页面加载完成...
 第 2 页加载完成，开始处理...
 
=== 处理第 2 页 ===
 查找订单，模式: 已退款未打印...
 订单 2894577205124381395 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2894577205124381395
 订单 4701624266644991014 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4701624266644991014
 订单 2894547218807163251 有 2 个真正的商品子订单
 子订单 1 未退款 - 订单号: 2894547218807163251
 子订单 2 未退款 - 订单号: 2894547218807163251
 跳过全部子订单已退款的订单 - 订单号: 2894547218807163251
 订单 2894601108226567784 有 2 个真正的商品子订单
 子订单 1 未退款 - 订单号: 2894601108226567784
 子订单 2 未退款 - 订单号: 2894601108226567784
 跳过全部子订单已退款的订单 - 订单号: 2894601108226567784
 订单 2894512369490067457 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 2894512369490067457
 订单 2894371827256013959 有 3 个真正的商品子订单
 子订单 1 未退款 - 订单号: 2894371827256013959
 子订单 2 未退款 - 订单号: 2894371827256013959
 子订单 3 未退款 - 订单号: 2894371827256013959
 跳过全部子订单已退款的订单 - 订单号: 2894371827256013959
 订单 4701735252440785224 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4701735252440785224
 订单 4701727044721562524 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4701727044721562524
 订单 4701722256853089622 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4701722256853089622
 订单 4701460502303785524 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4701460502303785524
 订单 4701442862883400544 有 1 个真正的商品子订单
 跳过未退款单子订单 - 订单号: 4701442862883400544
 订单 6920922150971145350 有 1 个真正的商品子订单
 找到有效单子订单（已退款、未打印） - 订单号: 6920922150971145350
 订单 2894358146690729466 有 1 个真正的商品子订单
 找到有效单子订单（已退款、未打印） - 订单号: 2894358146690729466
 订单 2894113959302646473 有 1 个真正的商品子订单
content.js:399 跳过未退款单子订单 - 订单号: 2894113959302646473
content.js:330 订单 2894221274792641180 有 1 个真正的商品子订单
content.js:399 跳过未退款单子订单 - 订单号: 2894221274792641180
content.js:330 订单 2894185814863196179 有 1 个真正的商品子订单
content.js:391 找到有效单子订单（已退款、未打印） - 订单号: 2894185814863196179
content.js:330 订单 2894249100732518393 有 1 个真正的商品子订单
content.js:391 找到有效单子订单（已退款、未打印） - 订单号: 2894249100732518393
content.js:330 订单 4701295909699048124 有 1 个真正的商品子订单
content.js:391 找到有效单子订单（已退款、未打印） - 订单号: 4701295909699048124
content.js:330 订单 2894161225464810267 有 1 个真正的商品子订单
content.js:399 跳过未退款单子订单 - 订单号: 2894161225464810267
content.js:330 订单 2894194740813559166 有 1 个真正的商品子订单
content.js:399 跳过未退款单子订单 - 订单号: 2894194740813559166
content.js:330 订单 2894183328136375682 有 1 个真正的商品子订单
content.js:399 跳过未退款单子订单 - 订单号: 2894183328136375682
content.js:330 订单 4701351132451287912 有 1 个真正的商品子订单
content.js:399 跳过未退款单子订单 - 订单号: 4701351132451287912
content.js:484 共找到 5 个需要处理的订单
content.js:55 第 2 页找到 5 个需要处理的订单
content.js:60 处理第 2 页第 1 个订单 - 6920922150971145350 (single)
content.js:682 在页面中找到单号: 250825-332959618620582
content.js:699 在页面中找到上家快递: 777334807289085
content.js:718 在页面中找到仓库快递: 76734215887797
content.js:852 提取到成交时间: 2025-08-25
content.js:868 提取到商品ID: 3744486551988601224
content.js:591 提取到单号: 250825-332959618620582
content.js:592 提取到上家快递: 777334807289085
content.js:593 提取到仓库快递: 76734215887797
content.js:594 提取到商品ID: 3744486551988601224
content.js:595 提取到成交时间: 2025-08-25
content.js:599 获取上家快递物流信息: 777334807289085
content.js:1381 准备获取快递物流信息: 777334807289085 (测试模式: false)
content.js:1393 获取到物流信息: Object
content.js:608 订单处理完成: Object
content.js:60 处理第 2 页第 2 个订单 - 2894358146690729466 (single)
content.js:682 在页面中找到单号: 250825-468142026362193
content.js:699 在页面中找到上家快递: 9809207579248
content.js:718 在页面中找到仓库快递: YT7566930264692
content.js:852 提取到成交时间: 2025-08-25
content.js:868 提取到商品ID: 940944002020
content.js:591 提取到单号: 250825-468142026362193
content.js:592 提取到上家快递: 9809207579248
content.js:593 提取到仓库快递: YT7566930264692
content.js:594 提取到商品ID: 940944002020
content.js:595 提取到成交时间: 2025-08-25
content.js:599 获取上家快递物流信息: 9809207579248
content.js:1381 准备获取快递物流信息: 9809207579248 (测试模式: false)
content.js:1393 获取到物流信息: Object
content.js:608 订单处理完成: Object
content.js:60 处理第 2 页第 3 个订单 - 2894185814863196179 (single)
content.js:682 在页面中找到单号: 250825-259160839830024
content.js:699 在页面中找到上家快递: JT5407012136687
content.js:718 在页面中找到仓库快递: 773373903620519
content.js:852 提取到成交时间: 2025-08-25
content.js:868 提取到商品ID: 902151518115
content.js:591 提取到单号: 250825-259160839830024
content.js:592 提取到上家快递: JT5407012136687
content.js:593 提取到仓库快递: 773373903620519
content.js:594 提取到商品ID: 902151518115
content.js:595 提取到成交时间: 2025-08-25
content.js:599 获取上家快递物流信息: JT5407012136687
content.js:1381 准备获取快递物流信息: JT5407012136687 (测试模式: false)
content.js:1393 获取到物流信息: Object
content.js:608 订单处理完成: Object
content.js:60 处理第 2 页第 4 个订单 - 2894249100732518393 (single)
content.js:682 在页面中找到单号: 250825-330474462871619
content.js:699 在页面中找到上家快递: YT8790045653936
content.js:718 在页面中找到仓库快递: YT7566928550114
content.js:852 提取到成交时间: 2025-08-25
content.js:868 提取到商品ID: 963363788579
content.js:591 提取到单号: 250825-330474462871619
content.js:592 提取到上家快递: YT8790045653936
content.js:593 提取到仓库快递: YT7566928550114
content.js:594 提取到商品ID: 963363788579
content.js:595 提取到成交时间: 2025-08-25
content.js:599 获取上家快递物流信息: YT8790045653936
content.js:1381 准备获取快递物流信息: YT8790045653936 (测试模式: false)
content.js:1393 获取到物流信息: Object
content.js:608 订单处理完成: Object
content.js:60 处理第 2 页第 5 个订单 - 4701295909699048124 (single)
content.js:682 在页面中找到单号: 250825-250892807823459
content.js:699 在页面中找到上家快递: 9809168610648
content.js:718 在页面中找到仓库快递: YT7566929946048
content.js:852 提取到成交时间: 2025-08-25
content.js:868 提取到商品ID: 942078596755
content.js:591 提取到单号: 250825-250892807823459
content.js:592 提取到上家快递: 9809168610648
content.js:593 提取到仓库快递: YT7566929946048
content.js:594 提取到商品ID: 942078596755
content.js:595 提取到成交时间: 2025-08-25
content.js:599 获取上家快递物流信息: 9809168610648
content.js:1381 准备获取快递物流信息: 9809168610648 (测试模式: false)
content.js:1393 获取到物流信息: Object
content.js:608 订单处理完成: Object
content.js:1198 下一页按钮状态: 禁用
content.js:98 第 2 页处理完成，没有更多页面
content.js:103 
=== 所有页面处理完成 ===
content.js:104 总共处理了 2 页，提取了 11 个订单数据
content.js:106 执行完成！
content.js:1878 开始导出Excel文件...
content.js:1070 简略订单数据表创建完成，包含 0 条记录
content.js:2028 Excel文件已安全导出: 已退款未打印订单数据_20250828_2336.xlsx
/#/OrderNew:1 The file at 'blob:http://cloud.xingchenjisu.com/336f336d-a0db-4074-9277-751026a74f22' was loaded over an insecure connection. This file should be served over HTTPS.
