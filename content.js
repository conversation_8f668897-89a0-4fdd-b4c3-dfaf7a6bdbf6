// 订单数据抓取器
class OrderScraper {
    constructor() {
        this.orderData = [];
        this.currentPage = 1;
        this.isProcessing = false;
        this.totalOrders = 0;
        this.processedOrders = 0;
        this.progressDialog = null;
        this.statisticsData = {
            goodsSummary: new Map(), // 商品汇总：商品ID -> {totalCount, noSupplierCount, hasSupplierCount, orders}
            logisticsAnalysis: [], // 物流分析：{goodsId, supplierExpress, logisticsStatus, isDelivered, delayDays, orderNumber}
            timeDistribution: new Map() // 时间分布：日期 -> {totalCount, mainGoodsIds}
        };
    }

    // 开始执行抓取
    async execute(mode = 'unrefunded') {
        if (this.isProcessing) {
            console.log('正在处理中，请稍候...');
            return;
        }

        this.isProcessing = true;
        this.mode = mode; // 保存模式到实例变量
        console.log(`开始执行订单数据抓取，模式: ${mode}...`);

        try {
            // 创建进度提示框
            this.createProgressDialog();

            // 首先点击"已出单"标签页
            await this.clickShippedTab();

            // 等待页面加载
            await this.delay(2000);

            // 设置每页显示100条
            await this.setPageSize100();

            // 等待页面重新加载（100条订单需要更长时间）
            await this.delay(15000);

            // 3. 获取分页信息
            const pageInfo = this.getPageInfo();
            console.log(`开始处理 ${pageInfo.totalPages} 页订单数据`);

            // 4. 循环处理所有页面
            let currentPageNum = 1;
            while (true) {
                console.log(`\n=== 处理第 ${currentPageNum} 页 ===`);

                // 4.1 分析当前页的订单（筛选有效订单）
                const validOrdersData = await this.getUnprintedOrders();
                console.log(`第 ${currentPageNum} 页找到 ${validOrdersData.length} 个需要处理的订单`);

                // 4.2 处理当前页的每个有效订单
                for (let i = 0; i < validOrdersData.length; i++) {
                    const orderData = validOrdersData[i];
                    console.log(`处理第 ${currentPageNum} 页第 ${i + 1} 个订单 - ${orderData.orderNumber} (${orderData.type})`);

                    try {
                        if (orderData.type === 'single') {
                            // 单订单处理
                            await this.processSingleOrder(orderData.orderNumber, orderData.orderContainer);
                        } else {
                            // 多子订单处理
                            for (const subOrderInfo of orderData.validSubOrders) {
                                console.log(`处理子订单 ${subOrderInfo.index}`);
                                await this.processSubOrder(orderData.orderNumber, subOrderInfo.element, subOrderInfo.index, orderData.orderContainer);
                            }
                        }
                    } catch (error) {
                        console.error(`处理第 ${currentPageNum} 页订单 ${i + 1} 时出错:`, error);
                    }

                    // 添加延迟避免请求过快
                    await this.delay(1000);
                }

                // 4.3 检查是否有下一页
                if (await this.hasNextPageForOrders()) {
                    console.log(`第 ${currentPageNum} 页处理完成，准备跳转到下一页...`);

                    // 4.4 跳转到下一页
                    const success = await this.goToNextPageForOrders();
                    if (success) {
                        currentPageNum++;
                        // 等待页面加载（根据测试，需要至少15秒）
                        console.log(`已跳转到第 ${currentPageNum} 页，等待页面加载完成...`);
                        await this.delay(15000); // 等待页面完全加载（15秒）
                        console.log(`第 ${currentPageNum} 页加载完成，开始处理...`);
                    } else {
                        console.log('跳转下一页失败，结束处理');
                        break;
                    }
                } else {
                    console.log(`第 ${currentPageNum} 页处理完成，没有更多页面`);
                    break;
                }
            }

            console.log(`\n=== 所有页面处理完成 ===`);
            console.log(`总共处理了 ${currentPageNum} 页，提取了 ${this.orderData.length} 个订单数据`);

            console.log('执行完成！');

            // 导出Excel文件
            await this.exportToExcel();

            // 关闭进度提示框
            this.closeProgressDialog();

            alert(`执行完成！共处理 ${this.orderData.length} 个未打印订单，Excel文件已下载`);

        } catch (error) {
            console.error('执行过程中出错:', error);
            this.closeProgressDialog();
            alert(`执行失败: ${error.message}`);
        } finally {
            this.isProcessing = false;
        }
    }

    // 点击"已出单"标签页
    async clickShippedTab() {
        console.log('查找已出单标签页...');

        // 查找已出单按钮
        const shippedElements = document.querySelectorAll('span.print-icon.el-popover__reference');
        for (const element of shippedElements) {
            if (element.textContent.includes('已出单')) {
                console.log('找到已出单标签页，点击...');
                element.click();
                console.log('已点击已出单标签页');
                return;
            }
        }

        throw new Error('未找到已出单标签页');
    }

    // 点击"待确认"按钮（保留用于兼容）
    async clickPendingConfirmButton() {
        const buttons = document.querySelectorAll('.el-radio-button__inner');

        let targetButton = null;
        for (const button of buttons) {
            if (button.textContent.includes('待确认')) {
                targetButton = button;
                break;
            }
        }

        if (targetButton) {
            console.log('找到待确认按钮，点击...');
            targetButton.click();
        } else {
            throw new Error('未找到"待确认"按钮');
        }
    }

    // 设置每页显示100条
    async setPageSize100() {
        try {
            console.log('设置每页显示100条...');

            // 查找分页大小选择器
            const pageSizeSelector = document.querySelector('.el-pagination__sizes .el-select');
            if (!pageSizeSelector) {
                console.log('未找到分页大小选择器');
                return;
            }

            // 点击下拉框
            const selectInput = pageSizeSelector.querySelector('.el-input__inner');
            if (selectInput) {
                selectInput.click();

                // 等待下拉菜单出现
                await this.delay(500);

                // 查找100条/页选项
                const dropdownItems = document.querySelectorAll('.el-select-dropdown__item');
                let targetItem = null;

                for (const item of dropdownItems) {
                    if (item.textContent.includes('100条/页')) {
                        targetItem = item;
                        break;
                    }
                }

                if (targetItem) {
                    console.log('找到100条/页选项，点击...');
                    targetItem.click();
                    console.log('已设置为每页显示100条');
                } else {
                    console.log('未找到100条/页选项');
                }
            }
        } catch (error) {
            console.error('设置分页大小时出错:', error);
        }
    }

    // 检查是否有下一页（订单系统）
    async hasNextPageForOrders() {
        try {
            const nextButton = document.querySelector('.btn-next');
            const isDisabled = nextButton && nextButton.hasAttribute('disabled');
            console.log(`下一页按钮状态: ${nextButton ? (isDisabled ? '禁用' : '可用') : '不存在'}`);
            return nextButton && !isDisabled;
        } catch (error) {
            console.error('检查下一页时出错:', error);
            return false;
        }
    }

    // 跳转到下一页（订单系统）
    async goToNextPageForOrders() {
        try {
            const nextButton = document.querySelector('.btn-next');
            if (nextButton && !nextButton.hasAttribute('disabled')) {
                console.log('点击下一页按钮...');
                nextButton.click();
                this.currentPage++;
                console.log(`已跳转到第 ${this.currentPage} 页`);
                return true;
            }
            return false;
        } catch (error) {
            console.error('跳转下一页时出错:', error);
            return false;
        }
    }

    // 获取总页数和当前页信息
    getPageInfo() {
        try {
            // 获取总记录数
            const totalElement = document.querySelector('.el-pagination__total');
            let totalRecords = 0;
            if (totalElement) {
                const match = totalElement.textContent.match(/共\s*(\d+)\s*条/);
                if (match) {
                    totalRecords = parseInt(match[1]);
                }
            }

            // 获取当前页码
            const activePageElement = document.querySelector('.el-pager .number.active');
            const currentPage = activePageElement ? parseInt(activePageElement.textContent) : 1;

            // 计算总页数（假设每页100条）
            const totalPages = Math.ceil(totalRecords / 100);

            console.log(`分页信息 - 总记录: ${totalRecords}, 当前页: ${currentPage}, 总页数: ${totalPages}`);

            return {
                totalRecords,
                currentPage,
                totalPages
            };
        } catch (error) {
            console.error('获取分页信息时出错:', error);
            return {
                totalRecords: 0,
                currentPage: 1,
                totalPages: 1
            };
        }
    }

    // 获取所有需要处理的订单信息（使用.goodsInfo精准识别子订单）
    async getUnprintedOrders() {
        console.log('查找未打印且未完全退款的订单...');

        const allOrders = document.querySelectorAll('.tablePro');
        const validOrdersData = [];

        for (const orderContainer of allOrders) {
            // 获取订单编号（从订单头部）
            const orderNumberElement = orderContainer.querySelector('.tablePro-header a');
            const orderNumber = orderNumberElement ? orderNumberElement.textContent.trim() : '';

            // 检查订单是否有未打印的内容
            let hasUnprintedItems = false;
            const printButtons = orderContainer.querySelectorAll('span.print-icon.el-popover__reference');
            for (const button of printButtons) {
                if (button.textContent.trim() === '打') {
                    if (!button.classList.contains('done')) {
                        hasUnprintedItems = true;
                        break;
                    }
                }
            }

            // 如果没有未打印项目，跳过
            if (!hasUnprintedItems) {
                continue;
            }

            // 使用精准逻辑识别真正的子订单：有.goodsInfo + .goodsDes + 商品ID
            const goodsInfoElements = orderContainer.querySelectorAll('.goodsInfo');
            const realSubOrders = [];

            // 筛选出真正的商品子订单
            goodsInfoElements.forEach((info, index) => {
                const parentItem = info.closest('.tablePro-body-item');
                const goodsDesElements = parentItem ? parentItem.querySelectorAll('.goodsDes') : [];

                if (goodsDesElements.length > 0) {
                    // 检查是否有商品ID（确保是真正的商品，不是赠品/物流信息）
                    const hasGoodsId = Array.from(goodsDesElements).some(des =>
                        des.textContent.includes('商品ID：')
                    );

                    if (hasGoodsId) {
                        realSubOrders.push({
                            goodsInfo: info,
                            parentItem: parentItem,
                            index: realSubOrders.length + 1
                        });
                    }
                }
            });

            console.log(`订单 ${orderNumber} 有 ${realSubOrders.length} 个真正的商品子订单`);

            if (realSubOrders.length === 0) {
                // 没有真正的商品子订单，跳过
                console.log(`跳过无真正商品子订单的订单 - 订单号: ${orderNumber}`);
                continue;
            }

            // 🔥 精确检测失效单对应的子订单
            const invalidSubOrders = this.detectInvalidSubOrders(orderContainer, realSubOrders);

            if (realSubOrders.length === 1) {
                // 单个真正的子订单，检查退款状态和打印状态
                const subOrderData = realSubOrders[0];
                const goodsDesElements = subOrderData.parentItem.querySelectorAll('.goodsDes');

                // 检查退款状态
                let isRefunded = false;
                for (const element of goodsDesElements) {
                    if (element.textContent.includes('退款成功')) {
                        isRefunded = true;
                        break;
                    }
                }

                // 检查打印状态
                const printButtons = orderContainer.querySelectorAll('span.print-icon.el-popover__reference');
                const printButtonsArray = Array.from(printButtons).filter(btn => btn.textContent.trim() === '打');
                const isPrinted = printButtonsArray[0] ? printButtonsArray[0].classList.contains('done') : false;

                // 🔥 使用精确的失效单检测
                const isInvalid = invalidSubOrders.has(1);

                if (isRefunded) {
                    console.log(`跳过已退款单子订单 - 订单号: ${orderNumber}`);
                } else if (isPrinted) {
                    console.log(`跳过已打印单子订单 - 订单号: ${orderNumber}`);
                } else if (isInvalid) {
                    console.log(`跳过失效单子订单 - 订单号: ${orderNumber}`);
                } else {
                    console.log(`找到有效单子订单（未退款、未打印、非失效） - 订单号: ${orderNumber}`);
                    validOrdersData.push({
                        orderNumber,
                        orderContainer,
                        type: 'single',
                        validSubOrders: []
                    });
                }

            } else {
                // 多个真正的子订单，检查每个子订单的退款状态和打印状态
                const validSubOrders = [];

                // 获取所有"打"按钮
                const printButtons = orderContainer.querySelectorAll('span.print-icon.el-popover__reference');
                const printButtonsArray = Array.from(printButtons).filter(btn => btn.textContent.trim() === '打');

                for (const subOrderData of realSubOrders) {
                    const goodsDesElements = subOrderData.parentItem.querySelectorAll('.goodsDes');

                    // 检查退款状态
                    let isRefunded = false;
                    for (const element of goodsDesElements) {
                        if (element.textContent.includes('退款成功')) {
                            isRefunded = true;
                            break;
                        }
                    }

                    // 检查打印状态（按子订单索引对应打印按钮）
                    const printButtonIndex = subOrderData.index - 1; // 转换为0基索引
                    const isPrinted = printButtonsArray[printButtonIndex] ?
                        printButtonsArray[printButtonIndex].classList.contains('done') : false;

                    // 🔥 使用精确的失效单检测
                    const isInvalid = invalidSubOrders.has(subOrderData.index);

                    if (isRefunded) {
                        console.log(`子订单 ${subOrderData.index} 已退款 - 订单号: ${orderNumber}`);
                    } else if (isPrinted) {
                        console.log(`子订单 ${subOrderData.index} 已打印 - 订单号: ${orderNumber}`);
                    } else if (isInvalid) {
                        console.log(`子订单 ${subOrderData.index} 失效单 - 订单号: ${orderNumber}`);
                    } else {
                        console.log(`子订单 ${subOrderData.index} 有效（未退款、未打印、非失效） - 订单号: ${orderNumber}`);
                        validSubOrders.push({
                            element: subOrderData.parentItem,
                            goodsInfo: subOrderData.goodsInfo,
                            index: subOrderData.index
                        });
                    }
                }

                // 如果有有效子订单，添加到处理列表
                if (validSubOrders.length > 0) {
                    console.log(`找到有效多子订单 (${validSubOrders.length}/${realSubOrders.length}个有效) - 订单号: ${orderNumber}`);
                    validOrdersData.push({
                        orderNumber,
                        orderContainer,
                        type: 'multiple',
                        validSubOrders
                    });
                } else {
                    console.log(`跳过全部子订单已退款的订单 - 订单号: ${orderNumber}`);
                }
            }
        }

        console.log(`共找到 ${validOrdersData.length} 个需要处理的订单`);
        return validOrdersData;
    }

    // 🔥 精确检测失效单对应的子订单
    detectInvalidSubOrders(orderContainer, realSubOrders) {
        const invalidMarks = orderContainer.querySelectorAll('.invalid');
        const invalidSubOrders = new Set();

        for (const mark of invalidMarks) {
            if (mark.textContent.trim() === '失效单') {
                console.log(`🔍 发现失效单标记，开始精确定位...`);

                // 方法1: 通过位置关系判断
                const markRect = mark.getBoundingClientRect();
                console.log(`失效单标记位置: top=${markRect.top}, left=${markRect.left}`);

                let closestSubOrder = null;
                let minDistance = Infinity;

                realSubOrders.forEach((subOrder, index) => {
                    const subOrderRect = subOrder.parentItem.getBoundingClientRect();
                    console.log(`子订单${index + 1}位置: top=${subOrderRect.top}, left=${subOrderRect.left}`);

                    // 计算失效单标记与子订单的重叠程度和距离
                    const overlapVertical = Math.max(0, Math.min(markRect.bottom, subOrderRect.bottom) - Math.max(markRect.top, subOrderRect.top));
                    const overlapHorizontal = Math.max(0, Math.min(markRect.right, subOrderRect.right) - Math.max(markRect.left, subOrderRect.left));
                    const overlapArea = overlapVertical * overlapHorizontal;

                    // 计算中心点距离
                    const markCenterY = (markRect.top + markRect.bottom) / 2;
                    const markCenterX = (markRect.left + markRect.right) / 2;
                    const subOrderCenterY = (subOrderRect.top + subOrderRect.bottom) / 2;
                    const subOrderCenterX = (subOrderRect.left + subOrderRect.right) / 2;
                    const distance = Math.sqrt(Math.pow(markCenterX - subOrderCenterX, 2) + Math.pow(markCenterY - subOrderCenterY, 2));

                    console.log(`子订单${index + 1}: 重叠面积=${overlapArea}, 中心距离=${distance.toFixed(2)}`);

                    // 如果有重叠或距离最近，认为是对应的子订单
                    if (overlapArea > 0 || distance < minDistance) {
                        if (overlapArea > 0) {
                            console.log(`✅ 子订单${index + 1}与失效单标记有重叠，判定为失效单`);
                            closestSubOrder = index + 1;
                            minDistance = 0; // 重叠优先级最高
                        } else if (distance < minDistance) {
                            minDistance = distance;
                            closestSubOrder = index + 1;
                        }
                    }
                });

                if (closestSubOrder !== null) {
                    console.log(`🎯 最终判定：子订单${closestSubOrder}为失效单`);
                    invalidSubOrders.add(closestSubOrder);
                }

                // 方法2: 通过DOM结构关系判断（备用方案）
                if (closestSubOrder === null) {
                    console.log(`🔄 使用备用方案：通过DOM结构判断`);

                    // 查找失效单标记最近的父级容器
                    let currentElement = mark.parentElement;
                    while (currentElement && !currentElement.classList.contains('tablePro-body-item')) {
                        currentElement = currentElement.parentElement;
                    }

                    if (currentElement) {
                        // 找到对应的子订单索引
                        const targetItem = currentElement;
                        realSubOrders.forEach((subOrder, index) => {
                            if (subOrder.parentItem === targetItem) {
                                console.log(`🎯 通过DOM结构判定：子订单${index + 1}为失效单`);
                                invalidSubOrders.add(index + 1);
                            }
                        });
                    }
                }
            }
        }

        return invalidSubOrders;
    }

    // 处理单个订单（无子订单或只有一个子订单）
    async processSingleOrder(orderNumber, orderRow) {
        const order = {
            orderNumber: orderNumber,
            trackingNumber: '',
            supplierExpress: '',
            warehouseExpress: '',
            logistics: [],
            goodsId: '',
            dealTime: ''
        };

        try {
            // 直接在当前页面提取单号和快递信息
            const orderInfo = this.extractOrderInfoFromPage(orderRow);
            order.trackingNumber = orderInfo.trackingNumber;
            order.supplierExpress = orderInfo.supplierExpress;
            order.warehouseExpress = orderInfo.warehouseExpress;

            // 提取商品ID和成交时间
            const additionalInfo = this.extractAdditionalInfo(orderRow);
            order.goodsId = additionalInfo.goodsId;
            order.dealTime = additionalInfo.dealTime;

            console.log(`提取到单号: ${order.trackingNumber || '未找到'}`);
            console.log(`提取到上家快递: ${order.supplierExpress || '未找到'}`);
            console.log(`提取到仓库快递: ${order.warehouseExpress || '未找到'}`);
            console.log(`提取到商品ID: ${order.goodsId || '未找到'}`);
            console.log(`提取到成交时间: ${order.dealTime || '未找到'}`);

            // 获取物流信息
            if (order.supplierExpress) {
                console.log(`获取上家快递物流信息: ${order.supplierExpress}`);
                order.logistics = await this.getLogisticsInfo(order.supplierExpress);
            }

            this.orderData.push(order);

            // 收集统计数据
            this.collectStatisticsData(order);

            console.log('订单处理完成:', order);

        } catch (error) {
            console.error('处理单个订单时出错:', error);
            this.orderData.push(order);
        }
    }

    // 处理子订单 - 但使用完整订单容器提取信息
    async processSubOrder(orderNumber, subOrderElement, subOrderIndex, fullOrderContainer) {
        const order = {
            orderNumber: orderNumber,
            trackingNumber: '',
            supplierExpress: '',
            warehouseExpress: '',
            logistics: [],
            goodsId: '',
            dealTime: ''
        };

        try {
            // 使用新的按索引提取方法
            const subOrderInfo = this.extractOrderInfoBySubOrderIndex(fullOrderContainer, subOrderIndex);

            order.trackingNumber = subOrderInfo.trackingNumber;
            order.supplierExpress = subOrderInfo.supplierExpress;
            order.warehouseExpress = subOrderInfo.warehouseExpress;

            // 从子订单中提取商品ID，从订单容器中提取成交时间
            const subOrderAdditionalInfo = this.extractAdditionalInfoFromSubOrder(subOrderElement);
            const orderAdditionalInfo = this.extractAdditionalInfo(fullOrderContainer);

            order.goodsId = subOrderAdditionalInfo.goodsId;
            order.dealTime = orderAdditionalInfo.dealTime;

            console.log(`子订单${subOrderIndex} - 单号: ${order.trackingNumber || '未找到'}`);
            console.log(`子订单${subOrderIndex} - 上家快递: ${order.supplierExpress || '未找到'}`);
            console.log(`子订单${subOrderIndex} - 仓库快递: ${order.warehouseExpress || '未找到'}`);
            console.log(`子订单${subOrderIndex} - 商品ID: ${order.goodsId || '未找到'}`);
            console.log(`子订单${subOrderIndex} - 成交时间: ${order.dealTime || '未找到'}`);

            // 获取物流信息
            if (order.supplierExpress) {
                console.log(`获取子订单${subOrderIndex}上家快递物流信息: ${order.supplierExpress}`);
                order.logistics = await this.getLogisticsInfo(order.supplierExpress);
            }

            this.orderData.push(order);

            // 收集统计数据
            this.collectStatisticsData(order);

            console.log(`子订单${subOrderIndex}处理完成:`, order);

        } catch (error) {
            console.error(`处理子订单${subOrderIndex}时出错:`, error);
            this.orderData.push(order);
        }
    }

    // 从完整订单容器中提取信息（回滚到正常工作的版本）
    extractOrderInfoFromPage(orderRow) {
        const result = {
            trackingNumber: '',
            supplierExpress: '',
            warehouseExpress: ''
        };

        try {
            // 查找单号 - 格式通常是 XXXXXX-XXXXXXXXXXXX
            const allText = orderRow.textContent;
            const trackingNumberMatch = allText.match(/\d{6}-\d{15}/);
            if (trackingNumberMatch) {
                result.trackingNumber = trackingNumberMatch[0];
                console.log(`在页面中找到单号: ${result.trackingNumber}`);
            }

            // 查找上家快递 - 查找包含"上家快递"的文本并从链接中提取
            const allLinks = orderRow.querySelectorAll('a[href*="baidu.com"]');
            for (const link of allLinks) {
                const linkText = link.textContent.trim();
                const parentText = link.parentElement ? link.parentElement.textContent : '';

                // 检查是否是上家快递链接
                if (parentText.includes('上家快递') || linkText.match(/^[A-Za-z0-9]{10,}$/)) {
                    // 从href中提取快递单号
                    const href = link.getAttribute('href');
                    if (href && href.includes('wd=')) {
                        const match = href.match(/wd=([A-Za-z0-9]+)/);
                        if (match && !result.supplierExpress) {
                            result.supplierExpress = match[1].trim();
                            console.log(`在页面中找到上家快递: ${result.supplierExpress}`);
                        }
                    }
                }
            }

            // 查找仓库快递 - 查找包含"仓库快递"的文本并从链接中提取
            for (const link of allLinks) {
                const linkText = link.textContent.trim();
                const parentText = link.parentElement ? link.parentElement.textContent : '';

                // 检查是否是仓库快递链接
                if (parentText.includes('仓库快递') || (linkText.match(/^[A-Za-z0-9]{10,}$/) && result.supplierExpress && linkText !== result.supplierExpress)) {
                    // 从href中提取快递单号
                    const href = link.getAttribute('href');
                    if (href && href.includes('wd=')) {
                        const match = href.match(/wd=([A-Za-z0-9]+)/);
                        if (match && match[1] !== result.supplierExpress) {
                            result.warehouseExpress = match[1].trim();
                            console.log(`在页面中找到仓库快递: ${result.warehouseExpress}`);
                            break;
                        }
                    }
                }
            }

        } catch (error) {
            console.error('从页面提取订单信息时出错:', error);
        }

        return result;
    }

    // 从子订单元素中提取信息（改进版：支持多区域查找）
    extractOrderInfoFromSubOrder(subOrderElement) {
        const result = {
            trackingNumber: '',
            supplierExpress: '',
            warehouseExpress: ''
        };

        try {
            // 这个方法现在只是占位，实际逻辑移到了新的方法中
            // 因为子订单信息可能分布在订单的不同区域

        } catch (error) {
            console.error('从子订单提取信息时出错:', error);
        }

        return result;
    }

    // 新方法：从整个订单容器中按子订单索引提取对应信息
    extractOrderInfoBySubOrderIndex(orderContainer, subOrderIndex) {
        const result = {
            trackingNumber: '',
            supplierExpress: '',
            warehouseExpress: ''
        };

        try {
            // 获取所有单号
            const allTrackingNumbers = [];
            const allElements = orderContainer.querySelectorAll('*');

            for (const element of allElements) {
                const text = element.textContent;
                const match = text.match(/(\d{6}-\d{15})/);
                if (match) {
                    allTrackingNumbers.push(match[1]);
                }
            }

            // 去重并按出现顺序排列
            const uniqueTrackingNumbers = [...new Set(allTrackingNumbers)];
            console.log(`找到 ${uniqueTrackingNumbers.length} 个不同的单号:`, uniqueTrackingNumbers);

            // 根据子订单索引获取对应的单号
            if (subOrderIndex <= uniqueTrackingNumbers.length) {
                result.trackingNumber = uniqueTrackingNumbers[subOrderIndex - 1];
                console.log(`子订单${subOrderIndex}对应的单号: ${result.trackingNumber}`);
            }

            // 获取所有上家快递
            const allSupplierExpress = [];
            for (const element of allElements) {
                const text = element.textContent;
                if (text.includes('上家快递：')) {
                    const match = text.match(/上家快递：\s*([A-Za-z0-9]+)/);
                    if (match) {
                        allSupplierExpress.push(match[1]);
                    }
                }
            }

            const uniqueSupplierExpress = [...new Set(allSupplierExpress)];
            console.log(`找到 ${uniqueSupplierExpress.length} 个不同的上家快递:`, uniqueSupplierExpress);

            // 根据子订单索引获取对应的上家快递（如果存在）
            if (subOrderIndex <= uniqueSupplierExpress.length) {
                result.supplierExpress = uniqueSupplierExpress[subOrderIndex - 1];
                console.log(`子订单${subOrderIndex}对应的上家快递: ${result.supplierExpress}`);
            } else {
                console.log(`子订单${subOrderIndex}没有上家快递（上家未发货）`);
            }

            // 获取所有仓库快递
            const allWarehouseExpress = [];
            for (const element of allElements) {
                const text = element.textContent;
                if (text.includes('仓库快递：')) {
                    const match = text.match(/仓库快递：\s*([A-Za-z0-9]+)/);
                    if (match) {
                        allWarehouseExpress.push(match[1]);
                    }
                }
            }

            const uniqueWarehouseExpress = [...new Set(allWarehouseExpress)];
            console.log(`找到 ${uniqueWarehouseExpress.length} 个不同的仓库快递:`, uniqueWarehouseExpress);

            // 根据子订单索引获取对应的仓库快递
            if (subOrderIndex <= uniqueWarehouseExpress.length) {
                result.warehouseExpress = uniqueWarehouseExpress[subOrderIndex - 1];
                console.log(`子订单${subOrderIndex}对应的仓库快递: ${result.warehouseExpress}`);
            }

        } catch (error) {
            console.error('按索引提取子订单信息时出错:', error);
        }

        return result;
    }

    // 从订单容器中提取商品ID和成交时间
    extractAdditionalInfo(orderContainer) {
        const result = {
            goodsId: '',
            dealTime: ''
        };

        try {
            // 提取成交时间（从订单头部）
            const headerElements = orderContainer.querySelectorAll('.tablePro-header *');
            for (const element of headerElements) {
                const text = element.textContent;
                // 匹配日期格式：2025-08-14 或 2025年08月14日
                const dateMatch = text.match(/(\d{4}[-年]\d{2}[-月]\d{2})/);
                if (dateMatch) {
                    let dateStr = dateMatch[1];
                    // 统一格式为 YYYY-MM-DD
                    dateStr = dateStr.replace(/年/g, '-').replace(/月/g, '-').replace(/日/g, '');
                    result.dealTime = dateStr;
                    console.log(`提取到成交时间: ${result.dealTime}`);
                    break;
                }
            }

            // 提取商品ID（从第一个真正的子订单）
            const goodsInfoElements = orderContainer.querySelectorAll('.goodsInfo');
            for (const goodsInfo of goodsInfoElements) {
                const parentItem = goodsInfo.closest('.tablePro-body-item');
                if (parentItem) {
                    const goodsDesElements = parentItem.querySelectorAll('.goodsDes');
                    for (const des of goodsDesElements) {
                        if (des.textContent.includes('商品ID：')) {
                            const goodsIdMatch = des.textContent.match(/商品ID：\s*(\d+)/);
                            if (goodsIdMatch) {
                                result.goodsId = goodsIdMatch[1].trim();
                                console.log(`提取到商品ID: ${result.goodsId}`);
                                return result; // 找到第一个就返回
                            }
                        }
                    }
                }
            }

        } catch (error) {
            console.error('提取附加信息时出错:', error);
        }

        return result;
    }

    // 从子订单元素中提取商品ID
    extractAdditionalInfoFromSubOrder(subOrderElement) {
        const result = {
            goodsId: ''
        };

        try {
            // 从子订单的.goodsDes中提取商品ID
            const goodsDesElements = subOrderElement.querySelectorAll('.goodsDes');
            for (const des of goodsDesElements) {
                if (des.textContent.includes('商品ID：')) {
                    const goodsIdMatch = des.textContent.match(/商品ID：\s*(\d+)/);
                    if (goodsIdMatch) {
                        result.goodsId = goodsIdMatch[1].trim();
                        console.log(`从子订单提取到商品ID: ${result.goodsId}`);
                        break;
                    }
                }
            }

        } catch (error) {
            console.error('从子订单提取附加信息时出错:', error);
        }

        return result;
    }

    // 收集统计数据
    collectStatisticsData(order) {
        try {
            const goodsId = order.goodsId;
            const dealTime = order.dealTime;
            const hasSupplierExpress = !!(order.supplierExpress);

            // 1. 商品汇总统计
            if (goodsId) {
                if (!this.statisticsData.goodsSummary.has(goodsId)) {
                    this.statisticsData.goodsSummary.set(goodsId, {
                        totalCount: 0,
                        noSupplierCount: 0,
                        hasSupplierCount: 0,
                        deliveredCount: 0, // 已签收但未打印
                        inTransitCount: 0, // 运输中未打印
                        orders: []
                    });
                }

                const summary = this.statisticsData.goodsSummary.get(goodsId);
                summary.totalCount++;

                if (hasSupplierExpress) {
                    summary.hasSupplierCount++;
                } else {
                    summary.noSupplierCount++;
                }

                // 判断物流状态并分类统计
                let isDelivered = false;
                if (hasSupplierExpress && order.logistics && order.logistics.length > 0) {
                    const latestLogistics = order.logistics[0].message || '';
                    isDelivered = latestLogistics.includes('签收') ||
                                 latestLogistics.includes('派送成功') ||
                                 latestLogistics.includes('已代收') ||
                                 latestLogistics.includes('已取走') ||
                                 latestLogistics.includes('妥投') ||
                                 latestLogistics.includes('已投递') ||
                                 latestLogistics.includes('已送达');

                    if (isDelivered) {
                        summary.deliveredCount++;
                    } else {
                        summary.inTransitCount++;
                    }
                }

                summary.orders.push({
                    orderNumber: order.orderNumber,
                    dealTime: dealTime,
                    hasSupplierExpress: hasSupplierExpress,
                    supplierExpress: order.supplierExpress || '',
                    isDelivered: isDelivered
                });
            }

            // 2. 物流分析统计
            if (goodsId && hasSupplierExpress) {
                const logistics = order.logistics || [];
                let logisticsStatus = '无物流信息';
                let isDelivered = false;
                let delayDays = 0;

                if (logistics.length > 0) {
                    const latestLogistics = logistics[0]; // 最新物流信息
                    logisticsStatus = latestLogistics.message || '无状态';

                    // 更准确的签收识别逻辑
                    isDelivered = logisticsStatus.includes('签收') ||
                                 logisticsStatus.includes('派送成功') ||
                                 logisticsStatus.includes('已代收') ||
                                 logisticsStatus.includes('已取走') ||
                                 logisticsStatus.includes('妥投') ||
                                 logisticsStatus.includes('已投递') ||
                                 logisticsStatus.includes('已送达');

                    // 计算延迟天数（从成交时间到现在）
                    if (dealTime) {
                        const dealDate = new Date(dealTime);
                        const currentDate = new Date();
                        delayDays = Math.floor((currentDate - dealDate) / (1000 * 60 * 60 * 24));
                    }
                }

                this.statisticsData.logisticsAnalysis.push({
                    goodsId: goodsId,
                    supplierExpress: order.supplierExpress,
                    logisticsStatus: logisticsStatus,
                    isDelivered: isDelivered,
                    delayDays: delayDays,
                    orderNumber: order.orderNumber
                });
            }

            // 3. 时间分布统计
            if (dealTime) {
                if (!this.statisticsData.timeDistribution.has(dealTime)) {
                    this.statisticsData.timeDistribution.set(dealTime, {
                        totalCount: 0,
                        mainGoodsIds: new Set()
                    });
                }

                const timeData = this.statisticsData.timeDistribution.get(dealTime);
                timeData.totalCount++;
                if (goodsId) {
                    timeData.mainGoodsIds.add(goodsId);
                }
            }

        } catch (error) {
            console.error('收集统计数据时出错:', error);
        }
    }

    // 添加统计分析工作表
    addStatisticsSheets(workbook) {
        try {
            // 1. 商品问题汇总表
            this.addGoodsSummarySheet(workbook);

            // 2. 物流状态分析表
            this.addLogisticsAnalysisSheet(workbook);

            // 3. 时间分布分析表
            this.addTimeDistributionSheet(workbook);

        } catch (error) {
            console.error('添加统计工作表时出错:', error);
        }
    }

    // 商品问题汇总表
    addGoodsSummarySheet(workbook) {
        const wsData = [];

        // 表头
        wsData.push(['商品ID', '总未打印数', '无上家快递数', '已签收未打印数', '运输中未打印数', '问题等级']);

        // 数据处理
        const sortedGoods = Array.from(this.statisticsData.goodsSummary.entries())
            .sort((a, b) => b[1].totalCount - a[1].totalCount); // 按总数降序

        for (const [goodsId, summary] of sortedGoods) {
            let problemLevel = '正常';
            if (summary.totalCount >= 5) {
                problemLevel = '红色预警';
            } else if (summary.totalCount >= 3) {
                problemLevel = '黄色预警';
            } else if (summary.deliveredCount > 0) {
                problemLevel = '橙色警告'; // 已签收但未打印，仓库问题
            } else if (summary.noSupplierCount > 0) {
                problemLevel = '蓝色提醒'; // 无上家快递
            }

            wsData.push([
                goodsId,
                summary.totalCount,
                summary.noSupplierCount,
                summary.deliveredCount,
                summary.inTransitCount,
                problemLevel
            ]);
        }

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // 设置列宽
        ws['!cols'] = [
            { width: 20 }, // 商品ID
            { width: 15 }, // 总未打印数
            { width: 18 }, // 无上家快递数
            { width: 18 }, // 已签收未打印数
            { width: 18 }, // 运输中未打印数
            { width: 15 }  // 问题等级
        ];

        XLSX.utils.book_append_sheet(workbook, ws, '商品问题汇总');
    }

    // 物流状态分析表
    addLogisticsAnalysisSheet(workbook) {
        const wsData = [];

        // 表头
        wsData.push(['商品ID', '上家快递号', '物流最新状态', '是否已签收', '滞留天数', '订单编号']);

        // 数据处理
        const sortedLogistics = this.statisticsData.logisticsAnalysis
            .sort((a, b) => b.delayDays - a.delayDays); // 按滞留天数降序

        for (const item of sortedLogistics) {
            wsData.push([
                item.goodsId,
                item.supplierExpress,
                item.logisticsStatus.substring(0, 50), // 限制长度
                item.isDelivered ? '是' : '否',
                item.delayDays,
                item.orderNumber
            ]);
        }

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // 设置列宽
        ws['!cols'] = [
            { width: 20 }, // 商品ID
            { width: 20 }, // 上家快递号
            { width: 40 }, // 物流状态
            { width: 12 }, // 是否签收
            { width: 12 }, // 滞留天数
            { width: 25 }  // 订单编号
        ];

        XLSX.utils.book_append_sheet(workbook, ws, '物流状态分析');
    }

    // 时间分布分析表
    addTimeDistributionSheet(workbook) {
        const wsData = [];

        // 表头
        wsData.push(['成交日期', '该日期未打印总数', '主要问题商品数量']);

        // 数据处理
        const sortedTime = Array.from(this.statisticsData.timeDistribution.entries())
            .sort((a, b) => a[0].localeCompare(b[0])); // 按日期升序

        for (const [date, data] of sortedTime) {
            wsData.push([
                date,
                data.totalCount,
                data.mainGoodsIds.size
            ]);
        }

        const ws = XLSX.utils.aoa_to_sheet(wsData);

        // 设置列宽
        ws['!cols'] = [
            { width: 15 }, // 成交日期
            { width: 20 }, // 未打印总数
            { width: 20 }  // 主要问题商品数量
        ];

        XLSX.utils.book_append_sheet(workbook, ws, '时间分布分析');
    }

    // 检查是否有下一页（订单系统）
    async hasNextPageForOrders() {
        try {
            const nextButton = document.querySelector('.btn-next');
            const isDisabled = nextButton && nextButton.hasAttribute('disabled');
            console.log(`下一页按钮状态: ${nextButton ? (isDisabled ? '禁用' : '可用') : '不存在'}`);
            return nextButton && !isDisabled;
        } catch (error) {
            console.error('检查下一页时出错:', error);
            return false;
        }
    }

    // 跳转到下一页（订单系统）
    async goToNextPageForOrders() {
        try {
            const nextButton = document.querySelector('.btn-next');
            if (nextButton && !nextButton.hasAttribute('disabled')) {
                console.log('点击下一页按钮...');
                nextButton.click();
                this.currentPage++;
                console.log(`已跳转到第 ${this.currentPage} 页`);
                return true;
            }
            return false;
        } catch (error) {
            console.error('跳转下一页时出错:', error);
            return false;
        }
    }

    // 获取总页数和当前页信息
    getPageInfo() {
        try {
            // 获取总记录数
            const totalElement = document.querySelector('.el-pagination__total');
            let totalRecords = 0;
            if (totalElement) {
                const match = totalElement.textContent.match(/共\s*(\d+)\s*条/);
                if (match) {
                    totalRecords = parseInt(match[1]);
                }
            }

            // 获取当前页码
            const activePageElement = document.querySelector('.el-pager .number.active');
            const currentPage = activePageElement ? parseInt(activePageElement.textContent) : 1;

            // 计算总页数（假设每页100条）
            const totalPages = Math.ceil(totalRecords / 100);

            console.log(`分页信息 - 总记录: ${totalRecords}, 当前页: ${currentPage}, 总页数: ${totalPages}`);

            return {
                totalRecords,
                currentPage,
                totalPages
            };
        } catch (error) {
            console.error('获取分页信息时出错:', error);
            return {
                totalRecords: 0,
                currentPage: 1,
                totalPages: 1
            };
        }
    }

    // 处理当前页面的工单
    async processCurrentPage() {
        const rows = this.getWorkOrderRows();
        console.log(`当前页面找到 ${rows.length} 个工单`);

        for (let i = 0; i < rows.length; i++) {
            console.log(`处理第 ${i + 1} 个工单...`);
            await this.processWorkOrder(rows[i]);

            // 添加延迟避免操作过快
            await this.delay(1500);
        }

        // 检查是否有下一页
        if (await this.hasNextPage()) {
            await this.goToNextPage();
            await this.delay(3000); // 等待页面加载
            await this.processCurrentPage(); // 递归处理下一页
        }
    }

    // 获取工单行
    getWorkOrderRows() {
        const rows = document.querySelectorAll('.el-table__row');
        return Array.from(rows).filter(row => {
            // 确保行包含工单数据
            const cells = row.querySelectorAll('td');
            return cells.length >= 9; // 确保有足够的列
        });
    }

    // 处理单个工单
    async processWorkOrder(row) {
        try {
            this.processedOrders++;
            this.updateProgress();

            const workOrder = {
                orderNumber: '',
                trackingNumber: '',
                supplierExpress: '',
                warehouseExpress: '',
                communications: [],
                logistics: [] // 新增：仓库快递物流信息
            };

            // 提取订单编号
            const orderNumberCell = row.querySelector('td:nth-child(2) a');
            if (orderNumberCell) {
                workOrder.orderNumber = orderNumberCell.textContent.trim();
                console.log(`找到订单编号: ${workOrder.orderNumber}`);

                // 在新标签页打开订单详情并获取单号和快递信息
                const orderInfo = await this.getTrackingNumber(workOrder.orderNumber);
                workOrder.trackingNumber = orderInfo.trackingNumber;
                workOrder.supplierExpress = orderInfo.supplierExpress;
                workOrder.warehouseExpress = orderInfo.warehouseExpress;

                // 如果有仓库快递单号，获取物流信息
                if (workOrder.warehouseExpress) {
                    console.log(`获取仓库快递物流信息: ${workOrder.warehouseExpress}`);
                    workOrder.logistics = await this.getLogisticsInfo(workOrder.warehouseExpress);
                }
            }

            // 获取工单详情中的沟通记录
            workOrder.communications = await this.getCommunications(row);

            this.orderData.push(workOrder);
            console.log('工单处理完成:', workOrder);

        } catch (error) {
            console.error('处理工单时出错:', error);
        }
    }

    // 在新标签页获取单号和仓库快递信息
    async getTrackingNumber(orderNumber, testMode = false) {
        return new Promise((resolve) => {
            console.log(`准备打开新标签页获取订单信息: ${orderNumber} (测试模式: ${testMode})`);

            // 发送消息给background script打开新标签页
            chrome.runtime.sendMessage({
                action: 'openOrderTab',
                orderNumber: orderNumber,
                testMode: testMode
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('发送消息到background script失败:', chrome.runtime.lastError);
                    resolve({
                        trackingNumber: '',
                        warehouseExpress: ''
                    });
                    return;
                }

                if (response && response.trackingNumber) {
                    console.log(`获取到单号: ${response.trackingNumber}`);
                    console.log(`获取到上家快递: ${response.supplierExpress || '未找到'}`);
                    console.log(`获取到仓库快递: ${response.warehouseExpress || '未找到'}`);
                    resolve({
                        trackingNumber: response.trackingNumber,
                        supplierExpress: response.supplierExpress || '',
                        warehouseExpress: response.warehouseExpress || ''
                    });
                } else {
                    console.log('未能获取到单号，response:', response);
                    resolve({
                        trackingNumber: '',
                        supplierExpress: '',
                        warehouseExpress: ''
                    });
                }
            });
        });
    }

    // 获取仓库快递物流信息
    async getLogisticsInfo(expressNumber, testMode = false) {
        return new Promise((resolve) => {
            console.log(`准备获取快递物流信息: ${expressNumber} (测试模式: ${testMode})`);

            // 发送消息给background script获取物流信息
            chrome.runtime.sendMessage({
                action: 'getLogistics',
                expressNumber: expressNumber,
                testMode: testMode
            }, (response) => {
                if (chrome.runtime.lastError) {
                    console.error('获取物流信息失败:', chrome.runtime.lastError);
                    resolve([]);
                } else {
                    console.log(`获取到物流信息:`, response);
                    resolve(response.logistics || []);
                }
            });
        });
    }

    // 获取工单详情中的沟通记录
    async getCommunications(row) {
        return new Promise((resolve) => {
            try {
                // 找到详情按钮
                const buttons = row.querySelectorAll('td:last-child a');

                let targetButton = null;
                for (const button of buttons) {
                    if (button.textContent.includes('详情')) {
                        targetButton = button;
                        break;
                    }
                }

                if (!targetButton) {
                    console.log('未找到详情按钮');
                    resolve([]);
                    return;
                }

                // 点击详情按钮
                console.log('点击详情按钮...');
                targetButton.click();

                // 等待弹窗出现并提取沟通记录
                setTimeout(() => {
                    const communications = [];

                    // 查找沟通记录容器
                    const commentItems = document.querySelectorAll('.comment-item');

                    if (commentItems.length > 0) {
                        // 获取所有沟通记录（不限制数量）
                        Array.from(commentItems).forEach(item => {
                            const author = item.querySelector('.author')?.textContent?.trim() || '';
                            const time = item.querySelector('time')?.textContent?.trim() || '';
                            const content = item.querySelector('.comment-item-content')?.textContent?.trim() || '';

                            if (author && time && content) {
                                communications.push({
                                    author: author,
                                    time: time,
                                    content: content
                                });
                            }
                        });
                    }

                    console.log(`提取到 ${communications.length} 条沟通记录`);

                    // 关闭弹窗
                    const closeButton = document.querySelector('.el-dialog__headerbtn');
                    if (closeButton) {
                        closeButton.click();
                    }

                    resolve(communications);
                }, 2000); // 等待2秒让弹窗加载

            } catch (error) {
                console.error('获取沟通记录时出错:', error);
                resolve([]);
            }
        });
    }

    // 获取总工单数
    getTotalOrderCount() {
        const totalElement = document.querySelector('.el-pagination__total');
        if (totalElement) {
            const match = totalElement.textContent.match(/共\s*(\d+)\s*条/);
            if (match) {
                this.totalOrders = parseInt(match[1]);
                console.log(`总共有 ${this.totalOrders} 个工单`);
            }
        }
    }

    // 检查是否有下一页
    async hasNextPage() {
        const nextButton = document.querySelector('.btn-next');
        return nextButton && !nextButton.disabled;
    }

    // 跳转到下一页
    async goToNextPage() {
        const nextButton = document.querySelector('.btn-next');
        if (nextButton && !nextButton.disabled) {
            console.log('跳转到下一页...');
            nextButton.click();
            this.currentPage++;
            return true;
        }
        return false;
    }

    // 导出Excel文件
    async exportToExcel() {
        try {
            console.log('开始导出Excel文件...');

            // 创建工作簿
            const wb = XLSX.utils.book_new();

            // 设置工作簿默认样式
            wb.Props = {
                Title: '工单数据',
                Subject: '工单批阅助手导出',
                Author: '工单批阅助手'
            };

            // 创建工作表数据
            const wsData = [];

            // 添加表头
            wsData.push(['订单编号', '单号', '工单详情', '仓库快递物流']);

            // 处理每个工单数据
            this.orderData.forEach(workOrder => {
                const communications = workOrder.communications || [];
                const logistics = workOrder.logistics || [];

                // 第一行：订单编号、单号、第一条沟通记录和第一条物流信息
                wsData.push([
                    workOrder.orderNumber || '',
                    workOrder.trackingNumber || '',
                    communications.length > 0 ? `${communications[0].author} ${communications[0].time}\n${communications[0].content}` : '无沟通记录',
                    logistics.length > 0 ? `${logistics[0].time}\n${logistics[0].message}` : '无物流信息'
                ]);

                // 第二行：上家快递标签、上家快递单号、第二条沟通记录和第二条物流信息
                wsData.push([
                    '上家快递',
                    workOrder.supplierExpress || '',
                    communications.length > 1 ? `${communications[1].author} ${communications[1].time}\n${communications[1].content}` : '',
                    logistics.length > 1 ? `${logistics[1].time}\n${logistics[1].message}` : ''
                ]);

                // 第三行：仓库快递标签、仓库快递单号、第三条沟通记录和第三条物流信息
                wsData.push([
                    '仓库快递',
                    workOrder.warehouseExpress || '',
                    communications.length > 2 ? `${communications[2].author} ${communications[2].time}\n${communications[2].content}` : '',
                    logistics.length > 2 ? `${logistics[2].time}\n${logistics[2].message}` : ''
                ]);

                // 动态添加剩余的沟通记录（从第4条开始）
                const maxRows = Math.max(communications.length, logistics.length);
                for (let i = 3; i < maxRows; i++) {
                    wsData.push([
                        '', // 空白
                        '', // 空白
                        i < communications.length ? `${communications[i].author} ${communications[i].time}\n${communications[i].content}` : '',
                        i < logistics.length ? `${logistics[i].time}\n${logistics[i].message}` : ''
                    ]);
                }

                // 添加两行空白行分隔订单
                wsData.push(['', '', '', '']);
                wsData.push(['', '', '', '']);
            });

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(wsData);

            // 设置列宽
            ws['!cols'] = [
                { width: 28 }, // 订单编号列
                { width: 32 }, // 单号列
                { width: 112 }, // 工单详情列
                { width: 84 } // 仓库快递物流列
            ];

            // 设置单元格样式（使用xlsx-js-style语法）
            const range = XLSX.utils.decode_range(ws['!ref']);

            // 设置表头样式（第一行）- 微软雅黑粗体
            for (let col = 0; col < 4; col++) {
                const headerCell = ws[XLSX.utils.encode_cell({r: 0, c: col})];
                if (headerCell) {
                    headerCell.s = {
                        font: {
                            name: "Microsoft YaHei",
                            bold: true,
                            sz: 12,
                            color: { rgb: "000000" }
                        },
                        alignment: {
                            horizontal: 'center',
                            vertical: 'center'
                        },
                        fill: {
                            patternType: "solid",
                            fgColor: { rgb: "E6E6E6" }
                        }
                    };
                }
            }

            // 设置数据行样式 - 微软雅黑常规
            for (let row = 1; row <= range.e.r; row++) {
                for (let col = 0; col < 4; col++) {
                    const cell = ws[XLSX.utils.encode_cell({r: row, c: col})];
                    if (cell && cell.v) {
                        // 基础样式：微软雅黑字体
                        cell.s = {
                            font: {
                                name: "Microsoft YaHei",
                                sz: 11,
                                color: { rgb: "000000" }
                            },
                            alignment: {
                                wrapText: col === 2 || col === 3, // 工单详情列和物流信息列自动换行
                                vertical: 'top',
                                horizontal: col === 0 ? 'center' : 'left'
                            }
                        };

                        // 数字格式处理 - 确保订单编号和单号显示为文本
                        if (col < 2 && /^\d/.test(cell.v.toString())) {
                            cell.t = 's'; // 设置为字符串类型
                            cell.z = '@'; // 文本格式
                        }
                    }
                }
            }

            // 设置行高
            const rowHeights = [];
            for (let row = 0; row <= range.e.r; row++) {
                const detailCell = ws[XLSX.utils.encode_cell({r: row, c: 2})];
                if (detailCell && detailCell.v) {
                    // 根据内容长度估算行高，但最小35字符高度
                    const content = detailCell.v.toString();
                    const lines = content.split('\n').length;
                    const estimatedHeight = Math.max(35, lines * 18); // 每行约18像素，最小35
                    rowHeights[row] = { hpt: estimatedHeight };
                } else {
                    rowHeights[row] = { hpt: 35 }; // 默认行高35
                }
            }
            ws['!rows'] = rowHeights;

            // 添加工作表到工作簿
            XLSX.utils.book_append_sheet(wb, ws, '工单数据');

            // 生成文件名
            const now = new Date();
            const filename = `工单数据_${now.getFullYear()}${(now.getMonth()+1).toString().padStart(2,'0')}${now.getDate().toString().padStart(2,'0')}_${now.getHours().toString().padStart(2,'0')}${now.getMinutes().toString().padStart(2,'0')}.xlsx`;

            // 生成Excel文件的二进制数据（启用样式支持）
            const wbout = XLSX.write(wb, {
                bookType: 'xlsx',
                type: 'array',
                cellStyles: true,
                Props: {
                    Title: '工单数据',
                    Subject: '工单批阅助手导出',
                    Author: '工单批阅助手'
                }
            });

            // 创建Blob
            const blob = new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

            // 直接使用传统下载方法（更可靠）
            this.fallbackDownload(blob, filename);

        } catch (error) {
            console.error('导出Excel时出错:', error);
            throw new Error('导出Excel失败: ' + error.message);
        }
    }

    // 备用下载方法
    fallbackDownload(blob, filename) {
        try {
            // 方法1：使用Chrome扩展的downloads API
            if (chrome && chrome.runtime) {
                const url = URL.createObjectURL(blob);
                chrome.runtime.sendMessage({
                    action: 'downloadFile',
                    url: url,
                    filename: filename
                }, (response) => {
                    URL.revokeObjectURL(url);
                    if (response && response.success) {
                        console.log(`Excel文件已通过Chrome API导出: ${filename}`);
                    } else {
                        console.log('Chrome API下载失败，尝试传统方法');
                        this.traditionalDownload(blob, filename);
                    }
                });
            } else {
                this.traditionalDownload(blob, filename);
            }
        } catch (error) {
            console.error('下载方法失败:', error);
            this.traditionalDownload(blob, filename);
        }
    }

    // 传统下载方法
    traditionalDownload(blob, filename) {
        try {
            const url = URL.createObjectURL(blob);

            // 创建下载链接
            const downloadLink = document.createElement('a');
            downloadLink.href = url;
            downloadLink.download = filename;
            downloadLink.style.display = 'none';

            // 添加到页面并触发点击
            document.body.appendChild(downloadLink);

            // 使用鼠标事件来触发下载
            const clickEvent = new MouseEvent('click', {
                view: window,
                bubbles: true,
                cancelable: false
            });
            downloadLink.dispatchEvent(clickEvent);

            // 清理
            document.body.removeChild(downloadLink);

            // 延迟清理URL对象
            setTimeout(() => {
                URL.revokeObjectURL(url);
            }, 2000);

            console.log(`使用传统方法导出Excel文件: ${filename}`);

            // 显示提示信息
            alert(`Excel文件已生成：${filename}\n如果没有自动下载，请检查浏览器的下载设置或弹窗拦截设置。`);

        } catch (error) {
            console.error('传统下载方法也失败了:', error);
            alert(`文件导出失败: ${error.message}\n请尝试刷新页面后重新执行。`);
        }
    }

    // 创建进度提示框
    createProgressDialog() {
        // 创建简单的半透明提示框
        const dialog = document.createElement('div');
        dialog.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 30px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            text-align: center;
            min-width: 280px;
            z-index: 10000;
            border: 1px solid rgba(0, 0, 0, 0.1);
        `;

        dialog.innerHTML = `
            <div style="font-size: 16px; margin-bottom: 12px; color: #333; font-weight: 500;">正在处理工单数据</div>
            <div id="progress-text" style="font-size: 14px; color: #666; margin-bottom: 12px;">准备中...</div>
            <div style="width: 100%; height: 4px; background-color: #f0f0f0; border-radius: 2px; overflow: hidden;">
                <div id="progress-bar" style="height: 100%; background-color: #4CAF50; width: 0%; transition: width 0.3s ease;"></div>
            </div>
        `;

        document.body.appendChild(dialog);

        this.progressDialog = {
            overlay: dialog,
            textElement: dialog.querySelector('#progress-text'),
            barElement: dialog.querySelector('#progress-bar')
        };
    }

    // 更新进度
    updateProgress() {
        if (this.progressDialog && this.totalOrders > 0) {
            const percentage = Math.round((this.processedOrders / this.totalOrders) * 100);
            this.progressDialog.textElement.textContent = `正在保存第 ${this.processedOrders}/${this.totalOrders} 个订单`;
            this.progressDialog.barElement.style.width = `${percentage}%`;
        }
    }

    // 关闭进度提示框
    closeProgressDialog() {
        if (this.progressDialog) {
            document.body.removeChild(this.progressDialog.overlay);
            this.progressDialog = null;
        }
    }

    // 测试单个订单数据提取
    async testSingleOrder() {
        try {
            console.log('开始测试指定订单...');

            // 1. 点击已出单标签页
            await this.clickShippedTab();
            await this.delay(2000);

            // 2. 设置每页显示100条
            await this.setPageSize100();
            await this.delay(15000);

            // 3. 查找指定的测试订单
            const testOrderNumber = '2892244623343917185';
            console.log(`查找测试订单: ${testOrderNumber}`);

            const allOrders = document.querySelectorAll('.tablePro');
            let targetOrder = null;

            for (const order of allOrders) {
                const orderNumberElement = order.querySelector('.tablePro-header a');
                if (orderNumberElement && orderNumberElement.textContent.trim() === testOrderNumber) {
                    targetOrder = order;
                    console.log('找到测试订单');
                    break;
                }
            }

            if (!targetOrder) {
                alert(`未找到测试订单 ${testOrderNumber}，请确保该订单在当前页面中。`);
                return;
            }

            // 4. 处理找到的订单
            console.log('开始处理测试订单...');
            const order = {
                orderNumber: testOrderNumber,
                trackingNumber: '',
                supplierExpress: '',
                warehouseExpress: '',
                logistics: [],
                goodsId: '',
                dealTime: ''
            };

            // 5. 直接从页面提取单号和快递信息
            const orderInfo = this.extractOrderInfoFromPage(targetOrder);
            order.trackingNumber = orderInfo.trackingNumber;
            order.supplierExpress = orderInfo.supplierExpress;
            order.warehouseExpress = orderInfo.warehouseExpress;

            // 5.5. 提取商品ID和成交时间
            const additionalInfo = this.extractAdditionalInfo(targetOrder);
            order.goodsId = additionalInfo.goodsId;
            order.dealTime = additionalInfo.dealTime;

            // 6. 获取物流信息
            if (order.supplierExpress) {
                console.log(`获取上家快递物流信息: ${order.supplierExpress}`);
                order.logistics = await this.getLogisticsInfo(order.supplierExpress, true); // 测试模式
            }

            console.log('测试订单处理完成:', order);

            // 7. 显示测试结果
            const logisticsText = order.logistics.length > 0 ?
                `\n上家快递物流信息 (${order.logistics.length}条):\n${order.logistics.map((item, index) => `${index + 1}. ${item.time} - ${item.message.substring(0, 30)}...`).join('\n')}` :
                '\n上家快递物流信息: 未获取到';

            alert(`测试完成！\n订单编号: ${order.orderNumber}\n单号: ${order.trackingNumber || '未找到'}\n上家快递: ${order.supplierExpress || '未找到'}\n仓库快递: ${order.warehouseExpress || '未找到'}\n商品ID: ${order.goodsId || '未找到'}\n成交时间: ${order.dealTime || '未找到'}${logisticsText}\n\n请查看控制台获取详细日志。`);

        } catch (error) {
            console.error('测试过程中出错:', error);
            alert('测试过程中出错: ' + error.message);
        }
    }

    // 导出Excel文件
    async exportToExcel() {
        try {
            console.log('开始导出Excel文件...');

            // 创建工作表数据
            const wsData = [];

            // 添加表头
            wsData.push(['订单编号', '单号', '上家快递', '仓库快递', '上家快递物流']);

            // 处理每个订单数据（倒序显示，最旧的在上面）
            const reversedOrderData = [...this.orderData].reverse();
            reversedOrderData.forEach(order => {
                const logistics = order.logistics || [];

                // 第一行：订单编号、单号、上家快递、仓库快递和第一条物流信息
                wsData.push([
                    order.orderNumber || '',
                    order.trackingNumber || '',
                    order.supplierExpress || '',
                    order.warehouseExpress || '',
                    logistics.length > 0 ? `${logistics[0].time} ${logistics[0].message}` : '无物流信息'
                ]);

                // 第二行：商品ID、成交时间信息 + 第二条物流信息（如果有）
                wsData.push([
                    '商品ID：',
                    order.goodsId || '',
                    '成交时间：',
                    order.dealTime || '',
                    logistics.length > 1 ? `${logistics[1].time} ${logistics[1].message}` : '' // 第二条物流信息
                ]);

                // 添加剩余的物流信息（从第三条开始）
                for (let i = 2; i < logistics.length; i++) {
                    wsData.push([
                        '', // 空白
                        '', // 空白
                        '', // 空白
                        '', // 空白
                        `${logistics[i].time} ${logistics[i].message}`
                    ]);
                }

                // 添加两行空白行分隔订单
                wsData.push(['', '', '', '', '']);
                wsData.push(['', '', '', '', '']);
            });

            // 创建工作表
            const ws = XLSX.utils.aoa_to_sheet(wsData);

            // 设置列宽
            ws['!cols'] = [
                { width: 28 }, // 订单编号列
                { width: 32 }, // 单号列
                { width: 20 }, // 上家快递列
                { width: 20 }, // 仓库快递列
                { width: 155 } // 上家快递物流列（增加宽度，一目了然）
            ];

            // 设置样式
            const range = XLSX.utils.decode_range(ws['!ref']);

            // 设置表头样式（第一行）
            for (let col = 0; col < 5; col++) {
                const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });
                if (!ws[cellAddress]) ws[cellAddress] = {};
                ws[cellAddress].s = {
                    font: { name: '微软雅黑', bold: true, color: { rgb: "FFFFFF" } },
                    fill: { fgColor: { rgb: "808080" } }, // 灰色背景
                    alignment: { wrapText: false, vertical: 'center', horizontal: 'center' }
                    // 移除边框
                };
            }

            // 设置数据行样式（移除边框）
            for (let row = 1; row <= range.e.r; row++) {
                for (let col = 0; col < 5; col++) {
                    const cellAddress = XLSX.utils.encode_cell({ r: row, c: col });
                    if (!ws[cellAddress]) continue;

                    const cell = ws[cellAddress];
                    if (cell.v !== undefined && cell.v !== null && cell.v !== '') {
                        cell.s = {
                            font: { name: '微软雅黑' },
                            alignment: {
                                wrapText: false, // 不自动换行，因为我们手动分行了
                                vertical: 'top',
                                horizontal: col === 0 ? 'center' : 'left'
                            }
                            // 移除边框
                        };

                        // 数字格式处理
                        if (col < 2 && /^\d/.test(cell.v.toString())) {
                            cell.z = '@';
                        }
                    }
                }
            }

            // 创建工作簿
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, '未打印订单数据');

            // 添加统计分析工作表
            this.addStatisticsSheets(wb);

            // 生成文件名
            const now = new Date();
            const timestamp = now.getFullYear() +
                            String(now.getMonth() + 1).padStart(2, '0') +
                            String(now.getDate()).padStart(2, '0') + '_' +
                            String(now.getHours()).padStart(2, '0') +
                            String(now.getMinutes()).padStart(2, '0');
            const filename = `未打印订单数据_${timestamp}.xlsx`;

            // 使用更安全的下载方式
            try {
                // 生成二进制数据
                const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });

                // 创建Blob对象
                const blob = new Blob([wbout], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                });

                // 创建下载链接
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.style.display = 'none';

                // 添加到页面并触发下载
                document.body.appendChild(a);
                a.click();

                // 清理
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                console.log(`Excel文件已安全导出: ${filename}`);
            } catch (downloadError) {
                console.warn('安全下载失败，尝试备用方案:', downloadError);
                // 备用方案：使用原来的方法
                XLSX.writeFile(wb, filename);
                console.log(`Excel文件已导出（备用方案）: ${filename}`);
            }

        } catch (error) {
            console.error('导出Excel时出错:', error);
            alert('导出Excel时出错: ' + error.message);
        }
    }

    // 延迟函数
    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// 创建抓取器实例
const scraper = new OrderScraper();

// 监听来自popup的消息
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
    if (request.action === 'execute') {
        const mode = request.mode || 'unrefunded'; // 默认为未退款模式
        console.log(`收到执行命令，模式: ${mode}`);
        scraper.execute(mode);
        sendResponse({success: true});
    } else if (request.action === 'testSingleOrder') {
        // 测试单个订单的数据提取
        console.log('测试单个订单数据提取');
        scraper.testSingleOrder();
        sendResponse({success: true});
    }
    return true;
});

console.log('主页面订单抓取助手脚本已加载');
